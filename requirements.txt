# 系统权限文件删除工具 - 依赖和打包说明
# ================================================

# 必需依赖
psutil>=5.8.0

# 打包依赖
pyinstaller>=5.0.0

# 安装说明
# ================================================
# 1. 安装依赖:
#    pip install -r requirements.txt
#
# 2. 直接运行:
#    python 系统权限删除工具.py
#
# 3. 打包成exe:
#    build.bat  (推荐，自动处理图标)
#    或手动: pyinstaller --onefile --noconsole --icon=icon.ico --name "系统权限删除工具" 系统权限删除工具.py
#
# 4. 打包选项说明:
#    --onefile     : 打包成单个exe文件
#    --noconsole   : 无控制台窗口（纯图形界面）
#    --console     : 显示控制台窗口（便于调试）
#    --name        : 指定exe文件名
#    --icon        : 指定图标文件（可选）
#
# 5. 推荐打包命令:
#    # 使用批处理脚本（推荐，自动处理图标）
#    build.bat
#
#    # 手动打包（带图标）
#    pyinstaller --onefile --noconsole --icon=icon.ico --name "系统权限删除工具" 系统权限删除工具.py
#
#    # 调试版本（带控制台）
#    pyinstaller --onefile --console --icon=icon.ico --name "系统权限删除工具-调试版" 系统权限删除工具.py
#
# 6. 使用说明:
#    - 直接运行: 自动启动图形界面
#    - 命令行: python 系统权限删除工具.py "文件路径"
#    - 拖拽: 将文件拖到exe上
#
# 7. 功能特点:
#    ✅ 解决Windows系统权限删除问题
#    ✅ 图形界面操作简单直观
#    ✅ 支持批量文件删除
#    ✅ 详细的文件诊断功能
#    ✅ 完整的操作日志记录
#    ✅ 自动获取文件所有权
#    ✅ 多种删除策略自动尝试
#
# 8. 系统要求:
#    - Windows 7/8/10/11
#    - Python 3.6+
#    - 建议以管理员身份运行
#
# 9. 故障排除:
#    - 如果缺少psutil: pip install psutil
#    - 如果图形界面无法启动: 使用 --cli 参数强制命令行模式
#    - 如果删除失败: 确保以管理员身份运行
#    - 查看logs目录中的详细日志文件
