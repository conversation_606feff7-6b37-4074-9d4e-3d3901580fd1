#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试权限删除工具
用于测试系统权限删除功能
"""

import os
import sys
import subprocess
import time

def test_system_permission_delete():
    """测试系统权限删除工具"""
    print("🧪 测试系统权限删除工具")
    print("=" * 50)
    
    # 检查工具是否存在
    tool_path = "系统权限删除工具.py"
    if not os.path.exists(tool_path):
        print(f"❌ 工具文件不存在: {tool_path}")
        return False
    
    print(f"✅ 工具文件存在: {tool_path}")
    
    # 获取要测试的路径
    if len(sys.argv) > 1:
        test_path = sys.argv[1].strip('"\'')
    else:
        test_path = input("请输入要测试删除的路径: ").strip('"\'')
    
    if not test_path:
        print("❌ 未提供测试路径")
        return False
    
    if not os.path.exists(test_path):
        print(f"❌ 测试路径不存在: {test_path}")
        return False
    
    print(f"🎯 测试路径: {test_path}")
    
    # 确认测试
    confirm = input("\n⚠️ 确认使用系统权限删除工具测试？(y/n): ").lower()
    if confirm != 'y':
        print("❌ 取消测试")
        return False
    
    print("\n🚀 启动系统权限删除工具...")
    print("=" * 50)
    
    # 调用系统权限删除工具
    try:
        cmd = f'python "{tool_path}" "{test_path}"'
        result = subprocess.run(cmd, shell=True)
        
        if result.returncode == 0:
            print("\n✅ 工具执行完成")
        else:
            print(f"\n⚠️ 工具执行返回码: {result.returncode}")
        
        # 检查删除结果
        if not os.path.exists(test_path):
            print("🎉 删除成功！")
            return True
        else:
            print("❌ 文件/目录仍然存在")
            return False
            
    except Exception as e:
        print(f"❌ 执行工具时发生错误: {e}")
        return False

def main():
    """主函数"""
    print("🧪 快速测试权限删除工具")
    print("用于验证系统权限删除功能")
    print("=" * 50)
    
    # 运行测试
    success = test_system_permission_delete()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试成功！系统权限删除工具工作正常")
    else:
        print("❌ 测试失败，请检查工具或权限问题")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
