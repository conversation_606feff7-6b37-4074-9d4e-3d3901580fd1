#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows文件权限删除工具
解决"需要来自xxx的权限才能删除xxx文件"的问题
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import subprocess
import threading
import shutil
import stat
import ctypes
from pathlib import Path
import time
import logging
import json
from datetime import datetime
import traceback

# 尝试导入psutil，如果失败则使用基础功能
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ psutil未安装，部分高级功能将不可用")

class FilePermissionDeleter:
    def __init__(self, root):
        self.root = root
        self.root.title("文件权限删除工具 - 解决权限问题")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')

        # 初始化日志系统
        self.setup_logging()

        # 检查是否以管理员身份运行
        self.is_admin = self.check_admin()

        # 初始化变量
        self.selected_paths = []
        self.deletion_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': None,
            'end_time': None
        }

        self.setup_ui()

        # 记录启动信息
        self.logger.info(f"程序启动 - 管理员模式: {self.is_admin}")
        self.log("🚀 程序启动完成")

    def setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 设置日志文件名（按日期）
        log_filename = os.path.join(log_dir, f"deletion_log_{datetime.now().strftime('%Y%m%d')}.log")

        # 配置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(message)s'

        # 创建logger
        self.logger = logging.getLogger('FileDeleter')
        self.logger.setLevel(logging.DEBUG)

        # 清除已有的处理器
        self.logger.handlers.clear()

        # 文件处理器
        file_handler = logging.FileHandler(log_filename, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(log_format)
        file_handler.setFormatter(file_formatter)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)

        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        self.log_file_path = log_filename
        
    def check_admin(self):
        """检查是否以管理员身份运行"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🗑️ Windows文件权限删除工具", 
                              font=('微软雅黑', 16, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # 管理员状态提示
        status_frame = tk.Frame(self.root, bg='#f0f0f0')
        status_frame.pack(fill='x', padx=10, pady=5)
        
        admin_status = "✅ 管理员模式" if self.is_admin else "⚠️ 普通用户模式"
        admin_color = "#27ae60" if self.is_admin else "#e74c3c"
        
        status_label = tk.Label(status_frame, text=f"当前状态: {admin_status}", 
                               font=('微软雅黑', 10), fg=admin_color, bg='#f0f0f0')
        status_label.pack(anchor='w')
        
        if not self.is_admin:
            restart_btn = tk.Button(status_frame, text="以管理员身份重启", 
                                   command=self.restart_as_admin,
                                   bg='#e74c3c', fg='white', font=('微软雅黑', 9))
            restart_btn.pack(anchor='w', pady=2)
        
        # 文件选择区域
        select_frame = tk.LabelFrame(self.root, text="📁 选择要删除的文件/文件夹", 
                                    font=('微软雅黑', 11), bg='#f0f0f0')
        select_frame.pack(fill='x', padx=10, pady=5)
        
        btn_frame = tk.Frame(select_frame, bg='#f0f0f0')
        btn_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(btn_frame, text="选择文件", command=self.select_files,
                 bg='#3498db', fg='white', font=('微软雅黑', 10)).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="选择文件夹", command=self.select_folder,
                 bg='#9b59b6', fg='white', font=('微软雅黑', 10)).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="清空列表", command=self.clear_list,
                 bg='#95a5a6', fg='white', font=('微软雅黑', 10)).pack(side='left', padx=5)
        
        # 文件列表
        list_frame = tk.Frame(select_frame, bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.file_listbox = tk.Listbox(list_frame, font=('Consolas', 9), height=8)
        scrollbar = tk.Scrollbar(list_frame, orient='vertical', command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 删除选项
        options_frame = tk.LabelFrame(self.root, text="🔧 删除选项", 
                                     font=('微软雅黑', 11), bg='#f0f0f0')
        options_frame.pack(fill='x', padx=10, pady=5)
        
        self.force_delete = tk.BooleanVar(value=True)
        self.recursive_delete = tk.BooleanVar(value=True)
        self.backup_before_delete = tk.BooleanVar(value=False)
        
        tk.Checkbutton(options_frame, text="强制删除（忽略只读属性）", 
                      variable=self.force_delete, font=('微软雅黑', 9), 
                      bg='#f0f0f0').pack(anchor='w', padx=5)
        
        tk.Checkbutton(options_frame, text="递归删除文件夹内容", 
                      variable=self.recursive_delete, font=('微软雅黑', 9), 
                      bg='#f0f0f0').pack(anchor='w', padx=5)
        
        tk.Checkbutton(options_frame, text="删除前创建备份", 
                      variable=self.backup_before_delete, font=('微软雅黑', 9), 
                      bg='#f0f0f0').pack(anchor='w', padx=5)
        
        # 操作按钮
        action_frame = tk.Frame(self.root, bg='#f0f0f0')
        action_frame.pack(fill='x', padx=10, pady=10)

        self.delete_btn = tk.Button(action_frame, text="🗑️ 开始删除",
                                   command=self.start_deletion,
                                   bg='#e74c3c', fg='white',
                                   font=('微软雅黑', 12, 'bold'),
                                   height=2)
        self.delete_btn.pack(side='left', padx=5, fill='x', expand=True)

        # 诊断按钮
        self.diagnose_btn = tk.Button(action_frame, text="🔍 诊断文件",
                                     command=self.diagnose_files,
                                     bg='#f39c12', fg='white',
                                     font=('微软雅黑', 10))
        self.diagnose_btn.pack(side='right', padx=5)

        # 日志按钮
        self.log_btn = tk.Button(action_frame, text="📋 查看日志文件",
                                command=self.open_log_file,
                                bg='#3498db', fg='white',
                                font=('微软雅黑', 10))
        self.log_btn.pack(side='right', padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress.pack(fill='x', padx=10, pady=5)
        
        # 日志输出
        log_frame = tk.LabelFrame(self.root, text="📋 操作日志", 
                                 font=('微软雅黑', 11), bg='#f0f0f0')
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
    def log(self, message, level="INFO"):
        """添加日志信息到GUI和文件"""
        timestamp = time.strftime("%H:%M:%S")
        gui_message = f"[{timestamp}] {message}"

        # 添加到GUI
        self.log_text.insert(tk.END, f"{gui_message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

        # 添加到文件日志
        if level.upper() == "ERROR":
            self.logger.error(message)
        elif level.upper() == "WARNING":
            self.logger.warning(message)
        elif level.upper() == "DEBUG":
            self.logger.debug(message)
        else:
            self.logger.info(message)
        
    def restart_as_admin(self):
        """以管理员身份重启程序"""
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, f'"{__file__}"', None, 1
            )
            self.root.quit()
        except Exception as e:
            messagebox.showerror("错误", f"无法以管理员身份重启: {e}")
    
    def select_files(self):
        """选择文件"""
        files = filedialog.askopenfilenames(title="选择要删除的文件")
        for file in files:
            if file not in self.selected_paths:
                self.selected_paths.append(file)
                self.file_listbox.insert(tk.END, f"📄 {file}")
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择要删除的文件夹")
        if folder and folder not in self.selected_paths:
            self.selected_paths.append(folder)
            self.file_listbox.insert(tk.END, f"📁 {folder}")
    
    def clear_list(self):
        """清空选择列表"""
        self.selected_paths.clear()
        self.file_listbox.delete(0, tk.END)
        self.log("已清空文件列表")

    def open_log_file(self):
        """打开日志文件"""
        try:
            os.startfile(self.log_file_path)
        except Exception as e:
            messagebox.showerror("错误", f"无法打开日志文件: {e}")

    def diagnose_files(self):
        """诊断选中的文件"""
        if not self.selected_paths:
            messagebox.showwarning("警告", "请先选择要诊断的文件或文件夹！")
            return

        self.log("🔍 开始文件诊断...")

        for path in self.selected_paths:
            self.diagnose_single_file(path)

    def diagnose_single_file(self, path):
        """诊断单个文件的详细信息"""
        try:
            self.log(f"📋 诊断文件: {path}")

            if not os.path.exists(path):
                self.log(f"❌ 文件不存在: {path}", "ERROR")
                return

            # 基本信息
            stat_info = os.stat(path)
            self.log(f"  📊 大小: {stat_info.st_size} 字节")
            self.log(f"  📅 修改时间: {datetime.fromtimestamp(stat_info.st_mtime)}")

            # 权限信息
            is_readonly = not (stat_info.st_mode & stat.S_IWRITE)
            self.log(f"  🔒 只读属性: {'是' if is_readonly else '否'}")

            # 检查文件是否被占用
            if os.path.isfile(path):
                if PSUTIL_AVAILABLE:
                    processes = self.get_processes_using_file(path)
                    if processes:
                        self.log(f"  ⚠️ 文件被以下进程占用:")
                        for proc in processes:
                            self.log(f"    - {proc['name']} (PID: {proc['pid']})")
                    else:
                        self.log(f"  ✅ 文件未被占用")
                else:
                    self.log(f"  ℹ️ 进程检测功能不可用（需要psutil）")

            # 检查父目录权限
            parent_dir = os.path.dirname(path)
            if os.path.exists(parent_dir):
                parent_stat = os.stat(parent_dir)
                parent_readonly = not (parent_stat.st_mode & stat.S_IWRITE)
                self.log(f"  📁 父目录只读: {'是' if parent_readonly else '否'}")

            self.log(f"  ✅ 诊断完成")

        except Exception as e:
            self.log(f"❌ 诊断失败: {path} - {str(e)}", "ERROR")
            self.logger.error(f"诊断失败详情: {traceback.format_exc()}")

    def get_processes_using_file(self, filepath):
        """获取正在使用文件的进程列表"""
        processes = []

        if not PSUTIL_AVAILABLE:
            self.logger.debug("psutil不可用，跳过进程检测")
            return processes

        try:
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    for item in proc.open_files():
                        if item.path.lower() == filepath.lower():
                            processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name']
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self.logger.debug(f"获取进程信息失败: {e}")

        return processes

    def start_deletion(self):
        """开始删除操作"""
        if not self.selected_paths:
            messagebox.showwarning("警告", "请先选择要删除的文件或文件夹！")
            return

        # 确认删除
        count = len(self.selected_paths)
        if not messagebox.askyesno("确认删除",
                                  f"确定要删除选中的 {count} 个项目吗？\n\n"
                                  "此操作不可撤销！"):
            return

        # 禁用删除按钮
        self.delete_btn.config(state='disabled')
        self.progress.start()

        # 在新线程中执行删除
        threading.Thread(target=self.delete_files, daemon=True).start()

    def delete_files(self):
        """删除文件的主要逻辑"""
        self.deletion_stats['start_time'] = datetime.now()
        self.deletion_stats['total'] = len(self.selected_paths)
        success_count = 0
        error_count = 0
        skipped_count = 0

        self.log("🚀 开始删除操作...")
        self.logger.info(f"开始删除操作，共 {len(self.selected_paths)} 个项目")

        for i, path in enumerate(self.selected_paths, 1):
            try:
                self.log(f"[{i}/{len(self.selected_paths)}] 正在处理: {path}")
                self.logger.info(f"处理项目 {i}/{len(self.selected_paths)}: {path}")

                if not os.path.exists(path):
                    self.log(f"⚠️ 路径不存在，跳过: {path}", "WARNING")
                    skipped_count += 1
                    continue

                # 诊断文件状态
                self.diagnose_before_delete(path)

                # 备份（如果选择）
                if self.backup_before_delete.get():
                    self.create_backup(path)

                # 尝试多种删除方法
                deleted = self.try_multiple_delete_methods(path)

                if deleted:
                    self.log(f"✅ 成功删除: {path}")
                    self.logger.info(f"成功删除: {path}")
                    success_count += 1
                else:
                    self.log(f"❌ 所有删除方法都失败: {path}", "ERROR")
                    self.logger.error(f"所有删除方法都失败: {path}")
                    error_count += 1

            except Exception as e:
                self.log(f"❌ 处理异常: {path} - {str(e)}", "ERROR")
                self.logger.error(f"处理异常: {path} - {str(e)}\n{traceback.format_exc()}")
                error_count += 1

        # 更新统计信息
        self.deletion_stats['success'] = success_count
        self.deletion_stats['failed'] = error_count
        self.deletion_stats['skipped'] = skipped_count
        self.deletion_stats['end_time'] = datetime.now()

        # 完成后的处理
        self.root.after(0, self.deletion_completed, success_count, error_count, skipped_count)

    def diagnose_before_delete(self, path):
        """删除前的快速诊断"""
        try:
            # 检查文件占用
            if os.path.isfile(path) and PSUTIL_AVAILABLE:
                processes = self.get_processes_using_file(path)
                if processes:
                    self.log(f"  ⚠️ 文件被占用，尝试结束进程...")
                    for proc in processes:
                        try:
                            p = psutil.Process(proc['pid'])
                            p.terminate()
                            self.log(f"  🔄 已终止进程: {proc['name']} (PID: {proc['pid']})")
                        except:
                            self.log(f"  ❌ 无法终止进程: {proc['name']} (PID: {proc['pid']})")

            # 检查只读属性
            if os.path.exists(path):
                stat_info = os.stat(path)
                if not (stat_info.st_mode & stat.S_IWRITE):
                    self.log(f"  🔓 检测到只读属性，准备移除...")

        except Exception as e:
            self.logger.debug(f"删除前诊断失败: {e}")

    def try_multiple_delete_methods(self, path):
        """尝试多种删除方法"""
        methods = [
            ("标准Python删除", self.method_python_delete),
            ("移除只读后删除", self.method_remove_readonly_delete),
            ("系统命令删除", self.method_system_command_delete),
            ("PowerShell删除", self.method_powershell_delete),
            ("强制结束进程后删除", self.method_kill_processes_delete),
            ("使用takeown获取所有权", self.method_takeown_delete),
            ("使用robocopy清空", self.method_robocopy_delete),
            ("移动到临时目录", self.method_move_to_temp),
        ]

        for method_name, method_func in methods:
            try:
                self.log(f"  🔄 尝试方法: {method_name}")
                if method_func(path):
                    self.log(f"  ✅ {method_name} 成功")
                    return True
                else:
                    self.log(f"  ❌ {method_name} 失败")
            except Exception as e:
                self.log(f"  ❌ {method_name} 异常: {str(e)}")
                self.logger.debug(f"{method_name} 异常详情: {traceback.format_exc()}")

        return False

    def method_python_delete(self, path):
        """方法1: 标准Python删除"""
        if os.path.isfile(path):
            os.remove(path)
        else:
            if self.recursive_delete.get():
                shutil.rmtree(path)
            else:
                os.rmdir(path)
        return not os.path.exists(path)

    def method_remove_readonly_delete(self, path):
        """方法2: 移除只读属性后删除"""
        if os.path.isfile(path):
            os.chmod(path, stat.S_IWRITE)
            os.remove(path)
        else:
            # 递归移除目录中所有文件的只读属性
            for root, dirs, files in os.walk(path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        os.chmod(file_path, stat.S_IWRITE)
                    except:
                        pass
                for dir in dirs:
                    dir_path = os.path.join(root, dir)
                    try:
                        os.chmod(dir_path, stat.S_IWRITE)
                    except:
                        pass

            if self.recursive_delete.get():
                shutil.rmtree(path, onerror=self.remove_readonly_error_handler)
            else:
                os.rmdir(path)

        return not os.path.exists(path)

    def method_system_command_delete(self, path):
        """方法3: 使用系统命令删除"""
        if os.path.isfile(path):
            cmd = f'del /f /q "{path}"'
        else:
            cmd = f'rmdir /s /q "{path}"'

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0 and not os.path.exists(path)

    def method_powershell_delete(self, path):
        """方法4: 使用PowerShell删除"""
        if os.path.isfile(path):
            cmd = f'powershell -Command "Remove-Item -Path \\"{path}\\" -Force"'
        else:
            cmd = f'powershell -Command "Remove-Item -Path \\"{path}\\" -Recurse -Force"'

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0 and not os.path.exists(path)

    def method_kill_processes_delete(self, path):
        """方法5: 强制结束相关进程后删除"""
        try:
            # 获取可能相关的进程名
            if "sogou" in path.lower():
                process_names = ["SogouCloud.exe", "SogouExe.exe", "SogouInput.exe",
                               "SogouTSF.exe", "SogouPY.exe", "SogouPinyin.exe"]
            elif "qq" in path.lower():
                process_names = ["QQ.exe", "QQProtect.exe", "QQExternal.exe"]
            else:
                # 通用进程名（基于路径）
                process_names = [f"{os.path.basename(path)}.exe"]

            # 结束进程
            for proc_name in process_names:
                try:
                    subprocess.run(f'taskkill /f /im "{proc_name}"',
                                 shell=True, capture_output=True)
                    self.log(f"  🔄 尝试结束进程: {proc_name}")
                except:
                    pass

            # 等待一下让进程完全结束
            time.sleep(2)

            # 再次尝试删除
            return self.method_system_command_delete(path)

        except Exception as e:
            self.logger.debug(f"强制结束进程删除失败: {e}")
            return False

    def method_takeown_delete(self, path):
        """方法6: 使用takeown获取文件所有权后删除"""
        try:
            self.log(f"    🔑 正在获取文件所有权...")

            # 步骤1: 获取文件所有权
            takeown_cmd = f'takeown /f "{path}" /r /d y'
            result1 = subprocess.run(takeown_cmd, shell=True, capture_output=True, text=True)

            if result1.returncode == 0:
                self.log(f"    ✅ 获取所有权成功")
            else:
                self.log(f"    ⚠️ 获取所有权部分失败: {result1.stderr}")

            # 步骤2: 给当前用户完全控制权限
            username = os.environ.get('USERNAME', 'Administrator')
            icacls_cmd = f'icacls "{path}" /grant "{username}":F /t /c /q'
            result2 = subprocess.run(icacls_cmd, shell=True, capture_output=True, text=True)

            if result2.returncode == 0:
                self.log(f"    ✅ 权限设置成功")
            else:
                self.log(f"    ⚠️ 权限设置部分失败: {result2.stderr}")

            # 步骤3: 尝试删除
            time.sleep(1)  # 等待权限生效

            # 先尝试移除只读属性
            self.remove_readonly_recursive(path)

            # 再尝试删除
            if os.path.isfile(path):
                os.remove(path)
                return not os.path.exists(path)
            else:
                shutil.rmtree(path, ignore_errors=True)
                if os.path.exists(path):
                    # 如果还存在，使用系统命令
                    cmd = f'rmdir /s /q "{path}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True)
                    return result.returncode == 0 and not os.path.exists(path)
                return True

        except Exception as e:
            self.logger.debug(f"takeown删除失败: {e}")
            return False

    def remove_readonly_recursive(self, path):
        """递归移除只读属性"""
        try:
            if os.path.isfile(path):
                os.chmod(path, stat.S_IWRITE)
            else:
                for root, dirs, files in os.walk(path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            os.chmod(file_path, stat.S_IWRITE)
                        except:
                            pass
                    for dir_name in dirs:
                        dir_path = os.path.join(root, dir_name)
                        try:
                            os.chmod(dir_path, stat.S_IWRITE)
                        except:
                            pass
        except Exception as e:
            self.logger.debug(f"移除只读属性失败: {e}")

    def method_robocopy_delete(self, path):
        """方法7: 使用robocopy清空目录"""
        if not os.path.isdir(path):
            return False

        try:
            import tempfile

            # 创建空目录
            empty_dir = tempfile.mkdtemp()

            # 使用robocopy镜像空目录到目标目录（清空目标目录）
            robocopy_cmd = f'robocopy "{empty_dir}" "{path}" /mir /r:0 /w:0'
            result = subprocess.run(robocopy_cmd, shell=True, capture_output=True, text=True)

            # 清理空目录
            try:
                os.rmdir(empty_dir)
            except:
                pass

            # 删除现在为空的目录
            try:
                os.rmdir(path)
                return True
            except:
                return False

        except Exception as e:
            self.logger.debug(f"robocopy删除失败: {e}")
            return False

    def method_move_to_temp(self, path):
        """方法8: 移动到临时目录（作为删除的替代方案）"""
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_name = f"deleted_{int(time.time())}_{os.path.basename(path)}"
        temp_path = os.path.join(temp_dir, temp_name)

        try:
            shutil.move(path, temp_path)
            self.log(f"  📁 已移动到临时目录: {temp_path}")
            return True
        except:
            return False

    def remove_readonly_error_handler(self, func, path, exc_info):
        """处理只读文件的错误处理器"""
        try:
            os.chmod(path, stat.S_IWRITE)
            func(path)
        except:
            pass

    def create_backup(self, path):
        """创建备份"""
        try:
            backup_dir = os.path.join(os.path.dirname(path), "backup_" +
                                    time.strftime("%Y%m%d_%H%M%S"))
            os.makedirs(backup_dir, exist_ok=True)

            backup_path = os.path.join(backup_dir, os.path.basename(path))

            if os.path.isfile(path):
                shutil.copy2(path, backup_path)
            else:
                shutil.copytree(path, backup_path)

            self.log(f"📦 已创建备份: {backup_path}")
        except Exception as e:
            self.log(f"⚠️ 备份失败: {str(e)}")

    def deletion_completed(self, success_count, error_count, skipped_count=0):
        """删除完成后的处理"""
        self.progress.stop()
        self.delete_btn.config(state='normal')

        # 计算耗时
        duration = self.deletion_stats['end_time'] - self.deletion_stats['start_time']
        duration_str = str(duration).split('.')[0]  # 去掉微秒

        total = success_count + error_count + skipped_count
        self.log(f"\n🎉 删除操作完成！")
        self.log(f"📊 统计信息:")
        self.log(f"  总计: {total} 项")
        self.log(f"  成功: {success_count} 项")
        self.log(f"  失败: {error_count} 项")
        self.log(f"  跳过: {skipped_count} 项")
        self.log(f"  耗时: {duration_str}")

        # 记录到文件日志
        self.logger.info(f"删除操作完成 - 总计:{total}, 成功:{success_count}, 失败:{error_count}, 跳过:{skipped_count}, 耗时:{duration_str}")

        # 生成操作报告
        self.generate_deletion_report()

        # 显示结果对话框
        if error_count == 0 and skipped_count == 0:
            messagebox.showinfo("完成", f"🎉 所有 {success_count} 个项目删除成功！\n耗时: {duration_str}")
        elif error_count == 0:
            messagebox.showinfo("完成", f"✅ 删除完成！\n成功: {success_count} 项\n跳过: {skipped_count} 项\n耗时: {duration_str}")
        else:
            messagebox.showwarning("部分完成",
                                 f"删除完成：\n"
                                 f"✅ 成功: {success_count} 项\n"
                                 f"❌ 失败: {error_count} 项\n"
                                 f"⚠️ 跳过: {skipped_count} 项\n"
                                 f"⏱️ 耗时: {duration_str}\n\n"
                                 f"请查看日志了解详情")

    def generate_deletion_report(self):
        """生成删除操作报告"""
        try:
            report_dir = "reports"
            if not os.path.exists(report_dir):
                os.makedirs(report_dir)

            report_file = os.path.join(report_dir, f"deletion_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

            # 处理datetime对象的序列化
            stats_copy = self.deletion_stats.copy()
            if stats_copy.get('start_time'):
                stats_copy['start_time'] = stats_copy['start_time'].isoformat()
            if stats_copy.get('end_time'):
                stats_copy['end_time'] = stats_copy['end_time'].isoformat()

            report_data = {
                "timestamp": datetime.now().isoformat(),
                "statistics": stats_copy,
                "files_processed": self.selected_paths,
                "system_info": {
                    "admin_mode": self.is_admin,
                    "python_version": sys.version,
                    "platform": sys.platform,
                    "psutil_available": PSUTIL_AVAILABLE
                }
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.log(f"📋 操作报告已保存: {report_file}")

        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")


def main():
    """主函数"""
    root = tk.Tk()
    app = FilePermissionDeleter(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    root.mainloop()


if __name__ == "__main__":
    main()
