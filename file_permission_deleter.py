#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows文件权限删除工具
解决"需要来自xxx的权限才能删除xxx文件"的问题
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import subprocess
import threading
import shutil
import stat
import ctypes
from pathlib import Path
import time

class FilePermissionDeleter:
    def __init__(self, root):
        self.root = root
        self.root.title("文件权限删除工具 - 解决权限问题")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # 检查是否以管理员身份运行
        self.is_admin = self.check_admin()
        
        self.setup_ui()
        self.selected_paths = []
        
    def check_admin(self):
        """检查是否以管理员身份运行"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🗑️ Windows文件权限删除工具", 
                              font=('微软雅黑', 16, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # 管理员状态提示
        status_frame = tk.Frame(self.root, bg='#f0f0f0')
        status_frame.pack(fill='x', padx=10, pady=5)
        
        admin_status = "✅ 管理员模式" if self.is_admin else "⚠️ 普通用户模式"
        admin_color = "#27ae60" if self.is_admin else "#e74c3c"
        
        status_label = tk.Label(status_frame, text=f"当前状态: {admin_status}", 
                               font=('微软雅黑', 10), fg=admin_color, bg='#f0f0f0')
        status_label.pack(anchor='w')
        
        if not self.is_admin:
            restart_btn = tk.Button(status_frame, text="以管理员身份重启", 
                                   command=self.restart_as_admin,
                                   bg='#e74c3c', fg='white', font=('微软雅黑', 9))
            restart_btn.pack(anchor='w', pady=2)
        
        # 文件选择区域
        select_frame = tk.LabelFrame(self.root, text="📁 选择要删除的文件/文件夹", 
                                    font=('微软雅黑', 11), bg='#f0f0f0')
        select_frame.pack(fill='x', padx=10, pady=5)
        
        btn_frame = tk.Frame(select_frame, bg='#f0f0f0')
        btn_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(btn_frame, text="选择文件", command=self.select_files,
                 bg='#3498db', fg='white', font=('微软雅黑', 10)).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="选择文件夹", command=self.select_folder,
                 bg='#9b59b6', fg='white', font=('微软雅黑', 10)).pack(side='left', padx=5)
        
        tk.Button(btn_frame, text="清空列表", command=self.clear_list,
                 bg='#95a5a6', fg='white', font=('微软雅黑', 10)).pack(side='left', padx=5)
        
        # 文件列表
        list_frame = tk.Frame(select_frame, bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.file_listbox = tk.Listbox(list_frame, font=('Consolas', 9), height=8)
        scrollbar = tk.Scrollbar(list_frame, orient='vertical', command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 删除选项
        options_frame = tk.LabelFrame(self.root, text="🔧 删除选项", 
                                     font=('微软雅黑', 11), bg='#f0f0f0')
        options_frame.pack(fill='x', padx=10, pady=5)
        
        self.force_delete = tk.BooleanVar(value=True)
        self.recursive_delete = tk.BooleanVar(value=True)
        self.backup_before_delete = tk.BooleanVar(value=False)
        
        tk.Checkbutton(options_frame, text="强制删除（忽略只读属性）", 
                      variable=self.force_delete, font=('微软雅黑', 9), 
                      bg='#f0f0f0').pack(anchor='w', padx=5)
        
        tk.Checkbutton(options_frame, text="递归删除文件夹内容", 
                      variable=self.recursive_delete, font=('微软雅黑', 9), 
                      bg='#f0f0f0').pack(anchor='w', padx=5)
        
        tk.Checkbutton(options_frame, text="删除前创建备份", 
                      variable=self.backup_before_delete, font=('微软雅黑', 9), 
                      bg='#f0f0f0').pack(anchor='w', padx=5)
        
        # 操作按钮
        action_frame = tk.Frame(self.root, bg='#f0f0f0')
        action_frame.pack(fill='x', padx=10, pady=10)
        
        self.delete_btn = tk.Button(action_frame, text="🗑️ 开始删除", 
                                   command=self.start_deletion,
                                   bg='#e74c3c', fg='white', 
                                   font=('微软雅黑', 12, 'bold'),
                                   height=2)
        self.delete_btn.pack(side='left', padx=5, fill='x', expand=True)
        
        # 进度条
        self.progress = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress.pack(fill='x', padx=10, pady=5)
        
        # 日志输出
        log_frame = tk.LabelFrame(self.root, text="📋 操作日志", 
                                 font=('微软雅黑', 11), bg='#f0f0f0')
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, 
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
    def log(self, message):
        """添加日志信息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def restart_as_admin(self):
        """以管理员身份重启程序"""
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, f'"{__file__}"', None, 1
            )
            self.root.quit()
        except Exception as e:
            messagebox.showerror("错误", f"无法以管理员身份重启: {e}")
    
    def select_files(self):
        """选择文件"""
        files = filedialog.askopenfilenames(title="选择要删除的文件")
        for file in files:
            if file not in self.selected_paths:
                self.selected_paths.append(file)
                self.file_listbox.insert(tk.END, f"📄 {file}")
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择要删除的文件夹")
        if folder and folder not in self.selected_paths:
            self.selected_paths.append(folder)
            self.file_listbox.insert(tk.END, f"📁 {folder}")
    
    def clear_list(self):
        """清空选择列表"""
        self.selected_paths.clear()
        self.file_listbox.delete(0, tk.END)
        self.log("已清空文件列表")

    def start_deletion(self):
        """开始删除操作"""
        if not self.selected_paths:
            messagebox.showwarning("警告", "请先选择要删除的文件或文件夹！")
            return

        # 确认删除
        count = len(self.selected_paths)
        if not messagebox.askyesno("确认删除",
                                  f"确定要删除选中的 {count} 个项目吗？\n\n"
                                  "此操作不可撤销！"):
            return

        # 禁用删除按钮
        self.delete_btn.config(state='disabled')
        self.progress.start()

        # 在新线程中执行删除
        threading.Thread(target=self.delete_files, daemon=True).start()

    def delete_files(self):
        """删除文件的主要逻辑"""
        success_count = 0
        error_count = 0

        self.log("开始删除操作...")

        for path in self.selected_paths:
            try:
                self.log(f"正在处理: {path}")

                if not os.path.exists(path):
                    self.log(f"⚠️ 路径不存在: {path}")
                    error_count += 1
                    continue

                # 备份（如果选择）
                if self.backup_before_delete.get():
                    self.create_backup(path)

                # 执行删除
                if os.path.isfile(path):
                    self.delete_file(path)
                else:
                    self.delete_directory(path)

                self.log(f"✅ 成功删除: {path}")
                success_count += 1

            except Exception as e:
                self.log(f"❌ 删除失败: {path} - {str(e)}")
                error_count += 1

                # 尝试使用系统命令强制删除
                if self.force_delete.get():
                    try:
                        self.force_delete_with_cmd(path)
                        self.log(f"✅ 强制删除成功: {path}")
                        success_count += 1
                        error_count -= 1
                    except Exception as e2:
                        self.log(f"❌ 强制删除也失败: {path} - {str(e2)}")

        # 完成后的处理
        self.root.after(0, self.deletion_completed, success_count, error_count)

    def delete_file(self, file_path):
        """删除单个文件"""
        # 移除只读属性
        if self.force_delete.get():
            try:
                os.chmod(file_path, stat.S_IWRITE)
            except:
                pass

        os.remove(file_path)

    def delete_directory(self, dir_path):
        """删除目录"""
        if self.recursive_delete.get():
            # 递归删除整个目录
            if self.force_delete.get():
                # 强制删除：移除所有文件的只读属性
                for root, dirs, files in os.walk(dir_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            os.chmod(file_path, stat.S_IWRITE)
                        except:
                            pass

            shutil.rmtree(dir_path)
        else:
            # 只删除空目录
            os.rmdir(dir_path)

    def force_delete_with_cmd(self, path):
        """使用系统命令强制删除"""
        if os.path.isfile(path):
            # 删除文件
            cmd = f'del /f /q "{path}"'
        else:
            # 删除目录
            cmd = f'rmdir /s /q "{path}"'

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"命令执行失败: {result.stderr}")

    def create_backup(self, path):
        """创建备份"""
        try:
            backup_dir = os.path.join(os.path.dirname(path), "backup_" +
                                    time.strftime("%Y%m%d_%H%M%S"))
            os.makedirs(backup_dir, exist_ok=True)

            backup_path = os.path.join(backup_dir, os.path.basename(path))

            if os.path.isfile(path):
                shutil.copy2(path, backup_path)
            else:
                shutil.copytree(path, backup_path)

            self.log(f"📦 已创建备份: {backup_path}")
        except Exception as e:
            self.log(f"⚠️ 备份失败: {str(e)}")

    def deletion_completed(self, success_count, error_count):
        """删除完成后的处理"""
        self.progress.stop()
        self.delete_btn.config(state='normal')

        total = success_count + error_count
        self.log(f"\n删除操作完成！")
        self.log(f"总计: {total} 项")
        self.log(f"成功: {success_count} 项")
        self.log(f"失败: {error_count} 项")

        if error_count == 0:
            messagebox.showinfo("完成", f"所有 {success_count} 个项目删除成功！")
        else:
            messagebox.showwarning("部分完成",
                                 f"删除完成：成功 {success_count} 项，失败 {error_count} 项\n"
                                 "请查看日志了解详情")


def main():
    """主函数"""
    root = tk.Tk()
    app = FilePermissionDeleter(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    root.mainloop()


if __name__ == "__main__":
    main()
