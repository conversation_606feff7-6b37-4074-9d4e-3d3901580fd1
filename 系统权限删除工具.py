#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统权限删除工具
专门解决Windows系统权限问题导致的文件无法删除
"""

import os
import sys
import subprocess
import time
import shutil
import stat
import ctypes
from pathlib import Path

def is_admin():
    """检查是否以管理员身份运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员身份重新运行"""
    if is_admin():
        return True
    else:
        print("⚠️ 需要管理员权限，正在尝试提升权限...")
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            print("❌ 无法获取管理员权限")
            return False

def take_ownership(path):
    """获取文件/目录的所有权"""
    print(f"🔑 正在获取所有权: {path}")
    
    try:
        # 方法1: 使用takeown命令
        takeown_cmd = f'takeown /f "{path}" /r /d y'
        result1 = subprocess.run(takeown_cmd, shell=True, capture_output=True, text=True)
        
        if result1.returncode == 0:
            print("  ✅ takeown 成功")
        else:
            print(f"  ⚠️ takeown 部分成功: {result1.stderr.strip()}")
        
        # 方法2: 给当前用户完全控制权限
        username = os.environ.get('USERNAME', 'Administrator')
        icacls_cmd = f'icacls "{path}" /grant "{username}":F /t /c /q'
        result2 = subprocess.run(icacls_cmd, shell=True, capture_output=True, text=True)
        
        if result2.returncode == 0:
            print("  ✅ 用户权限设置成功")
        else:
            print(f"  ⚠️ 用户权限设置部分成功")
        
        # 方法3: 给Administrators组完全控制权限
        admin_cmd = f'icacls "{path}" /grant Administrators:F /t /c /q'
        result3 = subprocess.run(admin_cmd, shell=True, capture_output=True, text=True)
        
        # 方法4: 给SYSTEM完全控制权限
        system_cmd = f'icacls "{path}" /grant SYSTEM:F /t /c /q'
        result4 = subprocess.run(system_cmd, shell=True, capture_output=True, text=True)
        
        # 方法5: 移除继承并重新设置权限
        reset_cmd = f'icacls "{path}" /inheritance:r /grant "{username}":F /t /c /q'
        result5 = subprocess.run(reset_cmd, shell=True, capture_output=True, text=True)
        
        print("  ✅ 权限设置完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 获取所有权失败: {e}")
        return False

def remove_readonly_recursive(path):
    """递归移除只读属性"""
    print(f"🔓 移除只读属性: {path}")
    
    try:
        if os.path.isfile(path):
            # 单个文件
            current_attr = os.stat(path).st_mode
            os.chmod(path, current_attr | stat.S_IWRITE)
        else:
            # 目录
            for root, dirs, files in os.walk(path):
                # 处理文件
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        current_attr = os.stat(file_path).st_mode
                        os.chmod(file_path, current_attr | stat.S_IWRITE)
                    except Exception as e:
                        print(f"    ⚠️ 无法修改文件属性: {file_path} - {e}")
                
                # 处理目录
                for dir in dirs:
                    dir_path = os.path.join(root, dir)
                    try:
                        current_attr = os.stat(dir_path).st_mode
                        os.chmod(dir_path, current_attr | stat.S_IWRITE)
                    except Exception as e:
                        print(f"    ⚠️ 无法修改目录属性: {dir_path} - {e}")
        
        print("  ✅ 只读属性移除完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 移除只读属性失败: {e}")
        return False

def force_delete_with_system_commands(path):
    """使用系统命令强制删除"""
    print(f"💻 使用系统命令删除: {path}")
    
    methods = [
        ("rmdir /s /q", f'rmdir /s /q "{path}"'),
        ("del /f /s /q", f'del /f /s /q "{path}\\*" && rmdir /s /q "{path}"'),
        ("PowerShell Remove-Item", f'powershell -Command "Remove-Item -Path \\"{path}\\" -Recurse -Force -ErrorAction SilentlyContinue"'),
        ("rd /s /q", f'rd /s /q "{path}"'),
    ]
    
    for method_name, cmd in methods:
        try:
            print(f"  🔄 尝试: {method_name}")
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if not os.path.exists(path):
                print(f"  ✅ {method_name} 成功")
                return True
            else:
                print(f"  ❌ {method_name} 失败")
                
        except Exception as e:
            print(f"  ❌ {method_name} 异常: {e}")
    
    return False

def robocopy_delete(path):
    """使用robocopy清空目录"""
    if not os.path.isdir(path):
        return False
    
    print(f"🔄 使用robocopy清空: {path}")
    
    try:
        import tempfile
        
        # 创建空目录
        empty_dir = tempfile.mkdtemp()
        
        # 使用robocopy镜像空目录到目标目录
        robocopy_cmd = f'robocopy "{empty_dir}" "{path}" /mir /r:0 /w:0 /np /nfl /ndl'
        result = subprocess.run(robocopy_cmd, shell=True, capture_output=True, text=True)
        
        # 清理空目录
        try:
            os.rmdir(empty_dir)
        except:
            pass
        
        # 删除现在为空的目录
        try:
            os.rmdir(path)
            if not os.path.exists(path):
                print("  ✅ robocopy删除成功")
                return True
        except:
            pass
        
        print("  ❌ robocopy删除失败")
        return False
        
    except Exception as e:
        print(f"  ❌ robocopy删除异常: {e}")
        return False

def ultimate_force_delete(path):
    """终极强制删除"""
    if not os.path.exists(path):
        print(f"❌ 路径不存在: {path}")
        return False
    
    print(f"🗑️ 开始终极强制删除: {path}")
    print("=" * 60)
    
    # 步骤1: 获取所有权
    take_ownership(path)
    time.sleep(1)
    
    # 步骤2: 移除只读属性
    remove_readonly_recursive(path)
    time.sleep(1)
    
    # 步骤3: 尝试Python标准删除
    print("🐍 尝试Python标准删除...")
    try:
        if os.path.isfile(path):
            os.remove(path)
        else:
            shutil.rmtree(path, ignore_errors=True)
        
        if not os.path.exists(path):
            print("  ✅ Python删除成功")
            return True
        else:
            print("  ❌ Python删除失败")
    except Exception as e:
        print(f"  ❌ Python删除异常: {e}")
    
    # 步骤4: 使用系统命令
    if force_delete_with_system_commands(path):
        return True
    
    # 步骤5: 使用robocopy（仅对目录）
    if os.path.isdir(path):
        if robocopy_delete(path):
            return True
    
    # 步骤6: 移动到临时目录
    print("📁 尝试移动到临时目录...")
    try:
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_name = f"deleted_{int(time.time())}_{os.path.basename(path)}"
        temp_path = os.path.join(temp_dir, temp_name)
        
        shutil.move(path, temp_path)
        print(f"  ✅ 已移动到: {temp_path}")
        print("  ℹ️ 重启后可手动删除临时文件")
        return True
        
    except Exception as e:
        print(f"  ❌ 移动失败: {e}")
    
    print("❌ 所有删除方法都失败了")
    return False

def main():
    """主函数"""
    print("🗑️ 系统权限删除工具")
    print("专门解决Windows系统权限问题")
    print("=" * 50)
    
    # 检查管理员权限
    if not is_admin():
        print("⚠️ 需要管理员权限才能处理系统权限问题")
        choice = input("是否以管理员身份重新运行？(y/n): ").lower()
        if choice == 'y':
            if not run_as_admin():
                sys.exit(1)
            return
        else:
            print("❌ 没有管理员权限，可能无法解决权限问题")
    else:
        print("✅ 管理员权限已获取")
    
    # 获取目标路径
    if len(sys.argv) > 1:
        target_path = sys.argv[1].strip('"\'')
    else:
        target_path = input("\n请输入要删除的文件或目录路径: ").strip('"\'')
    
    if not target_path:
        print("❌ 未提供路径")
        return
    
    print(f"\n🎯 目标路径: {target_path}")
    
    # 确认删除
    confirm = input("\n⚠️ 确认强制删除？此操作不可撤销！(y/n): ").lower()
    if confirm != 'y':
        print("❌ 取消删除")
        return
    
    print("\n🚀 开始删除过程...")
    print("=" * 50)
    
    # 执行删除
    start_time = time.time()
    success = ultimate_force_delete(target_path)
    end_time = time.time()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 删除成功！")
        print(f"⏱️ 耗时: {end_time - start_time:.2f}秒")
    else:
        print("❌ 删除失败")
        print("\n💡 建议:")
        print("   1. 重启计算机后重新运行")
        print("   2. 在安全模式下运行")
        print("   3. 检查是否有程序正在使用该文件")
        print("   4. 使用专业的文件解锁工具")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
