#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统权限文件删除工具 - 完整版
专门解决Windows系统权限问题导致的文件无法删除
整合了图形界面、命令行、批量处理等所有功能
"""

import os
import sys
import subprocess
import time
import shutil
import stat
import ctypes
import logging
import json
import tempfile
from pathlib import Path
from datetime import datetime
import traceback

# 尝试导入GUI相关模块
try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

# 尝试导入psutil
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

def is_admin():
    """检查是否以管理员身份运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员身份重新运行"""
    if is_admin():
        return True
    else:
        print("⚠️ 需要管理员权限，正在尝试提升权限...")
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            print("❌ 无法获取管理员权限")
            return False

def take_ownership(path):
    """获取文件/目录的所有权"""
    print(f"🔑 正在获取所有权: {path}")
    
    try:
        # 方法1: 使用takeown命令
        takeown_cmd = f'takeown /f "{path}" /r /d y'
        result1 = subprocess.run(takeown_cmd, shell=True, capture_output=True, text=True)
        
        if result1.returncode == 0:
            print("  ✅ takeown 成功")
        else:
            print(f"  ⚠️ takeown 部分成功: {result1.stderr.strip()}")
        
        # 方法2: 给当前用户完全控制权限
        username = os.environ.get('USERNAME', 'Administrator')
        icacls_cmd = f'icacls "{path}" /grant "{username}":F /t /c /q'
        result2 = subprocess.run(icacls_cmd, shell=True, capture_output=True, text=True)
        
        if result2.returncode == 0:
            print("  ✅ 用户权限设置成功")
        else:
            print(f"  ⚠️ 用户权限设置部分成功")
        
        # 方法3: 给Administrators组完全控制权限
        admin_cmd = f'icacls "{path}" /grant Administrators:F /t /c /q'
        result3 = subprocess.run(admin_cmd, shell=True, capture_output=True, text=True)
        
        # 方法4: 给SYSTEM完全控制权限
        system_cmd = f'icacls "{path}" /grant SYSTEM:F /t /c /q'
        result4 = subprocess.run(system_cmd, shell=True, capture_output=True, text=True)
        
        # 方法5: 移除继承并重新设置权限
        reset_cmd = f'icacls "{path}" /inheritance:r /grant "{username}":F /t /c /q'
        result5 = subprocess.run(reset_cmd, shell=True, capture_output=True, text=True)
        
        print("  ✅ 权限设置完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 获取所有权失败: {e}")
        return False

def remove_readonly_recursive(path):
    """递归移除只读属性"""
    print(f"🔓 移除只读属性: {path}")
    
    try:
        if os.path.isfile(path):
            # 单个文件
            current_attr = os.stat(path).st_mode
            os.chmod(path, current_attr | stat.S_IWRITE)
        else:
            # 目录
            for root, dirs, files in os.walk(path):
                # 处理文件
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        current_attr = os.stat(file_path).st_mode
                        os.chmod(file_path, current_attr | stat.S_IWRITE)
                    except Exception as e:
                        print(f"    ⚠️ 无法修改文件属性: {file_path} - {e}")
                
                # 处理目录
                for dir in dirs:
                    dir_path = os.path.join(root, dir)
                    try:
                        current_attr = os.stat(dir_path).st_mode
                        os.chmod(dir_path, current_attr | stat.S_IWRITE)
                    except Exception as e:
                        print(f"    ⚠️ 无法修改目录属性: {dir_path} - {e}")
        
        print("  ✅ 只读属性移除完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 移除只读属性失败: {e}")
        return False

def force_delete_with_system_commands(path):
    """使用系统命令强制删除"""
    print(f"💻 使用系统命令删除: {path}")
    
    methods = [
        ("rmdir /s /q", f'rmdir /s /q "{path}"'),
        ("del /f /s /q", f'del /f /s /q "{path}\\*" && rmdir /s /q "{path}"'),
        ("PowerShell Remove-Item", f'powershell -Command "Remove-Item -Path \\"{path}\\" -Recurse -Force -ErrorAction SilentlyContinue"'),
        ("rd /s /q", f'rd /s /q "{path}"'),
    ]
    
    for method_name, cmd in methods:
        try:
            print(f"  🔄 尝试: {method_name}")
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if not os.path.exists(path):
                print(f"  ✅ {method_name} 成功")
                return True
            else:
                print(f"  ❌ {method_name} 失败")
                
        except Exception as e:
            print(f"  ❌ {method_name} 异常: {e}")
    
    return False

def robocopy_delete(path):
    """使用robocopy清空目录"""
    if not os.path.isdir(path):
        return False
    
    print(f"🔄 使用robocopy清空: {path}")
    
    try:
        import tempfile
        
        # 创建空目录
        empty_dir = tempfile.mkdtemp()
        
        # 使用robocopy镜像空目录到目标目录
        robocopy_cmd = f'robocopy "{empty_dir}" "{path}" /mir /r:0 /w:0 /np /nfl /ndl'
        result = subprocess.run(robocopy_cmd, shell=True, capture_output=True, text=True)
        
        # 清理空目录
        try:
            os.rmdir(empty_dir)
        except:
            pass
        
        # 删除现在为空的目录
        try:
            os.rmdir(path)
            if not os.path.exists(path):
                print("  ✅ robocopy删除成功")
                return True
        except:
            pass
        
        print("  ❌ robocopy删除失败")
        return False
        
    except Exception as e:
        print(f"  ❌ robocopy删除异常: {e}")
        return False

def ultimate_force_delete(path):
    """终极强制删除"""
    if not os.path.exists(path):
        print(f"❌ 路径不存在: {path}")
        return False
    
    print(f"🗑️ 开始终极强制删除: {path}")
    print("=" * 60)
    
    # 步骤1: 获取所有权
    take_ownership(path)
    time.sleep(1)
    
    # 步骤2: 移除只读属性
    remove_readonly_recursive(path)
    time.sleep(1)
    
    # 步骤3: 尝试Python标准删除
    print("🐍 尝试Python标准删除...")
    try:
        if os.path.isfile(path):
            os.remove(path)
        else:
            shutil.rmtree(path, ignore_errors=True)
        
        if not os.path.exists(path):
            print("  ✅ Python删除成功")
            return True
        else:
            print("  ❌ Python删除失败")
    except Exception as e:
        print(f"  ❌ Python删除异常: {e}")
    
    # 步骤4: 使用系统命令
    if force_delete_with_system_commands(path):
        return True
    
    # 步骤5: 使用robocopy（仅对目录）
    if os.path.isdir(path):
        if robocopy_delete(path):
            return True
    
    # 步骤6: 移动到临时目录
    print("📁 尝试移动到临时目录...")
    try:
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_name = f"deleted_{int(time.time())}_{os.path.basename(path)}"
        temp_path = os.path.join(temp_dir, temp_name)
        
        shutil.move(path, temp_path)
        print(f"  ✅ 已移动到: {temp_path}")
        print("  ℹ️ 重启后可手动删除临时文件")
        return True
        
    except Exception as e:
        print(f"  ❌ 移动失败: {e}")
    
    print("❌ 所有删除方法都失败了")
    return False

class SystemPermissionDeleter:
    """系统权限删除工具类 - 整合所有功能"""

    def __init__(self):
        self.setup_logging()
        self.is_admin = is_admin()
        self.selected_paths = []
        self.deletion_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': None,
            'end_time': None
        }

    def setup_logging(self):
        """设置日志系统"""
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_filename = os.path.join(log_dir, f"system_deletion_log_{datetime.now().strftime('%Y%m%d')}.log")

        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

        self.logger = logging.getLogger('SystemDeleter')
        self.log_file_path = log_filename

    def log(self, message, level="INFO"):
        """记录日志"""
        print(message)
        if level.upper() == "ERROR":
            self.logger.error(message)
        elif level.upper() == "WARNING":
            self.logger.warning(message)
        elif level.upper() == "DEBUG":
            self.logger.debug(message)
        else:
            self.logger.info(message)

    def get_processes_using_file(self, filepath):
        """获取正在使用文件的进程列表"""
        processes = []

        if not PSUTIL_AVAILABLE:
            return processes

        try:
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    for item in proc.open_files():
                        if item.path.lower() == filepath.lower():
                            processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name']
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self.logger.debug(f"获取进程信息失败: {e}")

        return processes

    def kill_processes_using_file(self, filepath):
        """结束占用文件的进程"""
        if not PSUTIL_AVAILABLE:
            return False

        processes = self.get_processes_using_file(filepath)
        if not processes:
            return True

        self.log(f"  🔄 发现 {len(processes)} 个进程占用文件，尝试结束...")

        killed_count = 0
        for proc in processes:
            try:
                p = psutil.Process(proc['pid'])
                p.terminate()
                self.log(f"    ✅ 已终止: {proc['name']} (PID: {proc['pid']})")
                killed_count += 1
            except Exception as e:
                self.log(f"    ❌ 无法终止: {proc['name']} (PID: {proc['pid']}) - {e}")

        if killed_count > 0:
            time.sleep(2)  # 等待进程完全结束
            return True

        return False

    def diagnose_file(self, path):
        """诊断文件详细信息"""
        self.log(f"🔍 诊断文件: {path}")

        if not os.path.exists(path):
            self.log(f"  ❌ 文件不存在", "ERROR")
            return False

        try:
            # 基本信息
            stat_info = os.stat(path)
            self.log(f"  📊 大小: {stat_info.st_size} 字节")
            self.log(f"  📅 修改时间: {datetime.fromtimestamp(stat_info.st_mtime)}")

            # 权限信息
            is_readonly = not (stat_info.st_mode & stat.S_IWRITE)
            self.log(f"  🔒 只读属性: {'是' if is_readonly else '否'}")

            # 检查进程占用
            if os.path.isfile(path) and PSUTIL_AVAILABLE:
                processes = self.get_processes_using_file(path)
                if processes:
                    self.log(f"  ⚠️ 文件被 {len(processes)} 个进程占用:")
                    for proc in processes:
                        self.log(f"    - {proc['name']} (PID: {proc['pid']})")
                else:
                    self.log(f"  ✅ 文件未被占用")

            # 检查父目录权限
            parent_dir = os.path.dirname(path)
            if os.path.exists(parent_dir):
                parent_stat = os.stat(parent_dir)
                parent_readonly = not (parent_stat.st_mode & stat.S_IWRITE)
                self.log(f"  📁 父目录只读: {'是' if parent_readonly else '否'}")

            return True

        except Exception as e:
            self.log(f"  ❌ 诊断失败: {str(e)}", "ERROR")
            return False

    def batch_delete(self, paths):
        """批量删除文件"""
        self.deletion_stats['start_time'] = datetime.now()
        self.deletion_stats['total'] = len(paths)

        self.log(f"🚀 开始批量删除，共 {len(paths)} 个项目")

        success_count = 0
        failed_count = 0
        skipped_count = 0

        for i, path in enumerate(paths, 1):
            self.log(f"\n[{i}/{len(paths)}] 处理: {path}")

            if not os.path.exists(path):
                self.log(f"  ⚠️ 路径不存在，跳过", "WARNING")
                skipped_count += 1
                continue

            # 诊断文件
            self.diagnose_file(path)

            # 执行删除
            if ultimate_force_delete(path):
                self.log(f"  ✅ 删除成功")
                success_count += 1
            else:
                self.log(f"  ❌ 删除失败", "ERROR")
                failed_count += 1

        # 更新统计
        self.deletion_stats['success'] = success_count
        self.deletion_stats['failed'] = failed_count
        self.deletion_stats['skipped'] = skipped_count
        self.deletion_stats['end_time'] = datetime.now()

        # 生成报告
        self.generate_report()

        return success_count, failed_count, skipped_count

    def generate_report(self):
        """生成删除报告"""
        try:
            report_dir = "reports"
            if not os.path.exists(report_dir):
                os.makedirs(report_dir)

            report_file = os.path.join(report_dir, f"system_deletion_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

            stats_copy = self.deletion_stats.copy()
            if stats_copy.get('start_time'):
                stats_copy['start_time'] = stats_copy['start_time'].isoformat()
            if stats_copy.get('end_time'):
                stats_copy['end_time'] = stats_copy['end_time'].isoformat()

            report_data = {
                "timestamp": datetime.now().isoformat(),
                "statistics": stats_copy,
                "files_processed": self.selected_paths,
                "system_info": {
                    "admin_mode": self.is_admin,
                    "python_version": sys.version,
                    "platform": sys.platform,
                    "psutil_available": PSUTIL_AVAILABLE,
                    "gui_available": GUI_AVAILABLE
                }
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.log(f"📋 报告已保存: {report_file}")

        except Exception as e:
            self.log(f"❌ 生成报告失败: {e}", "ERROR")

def show_main_menu():
    """显示主菜单"""
    print("\n" + "=" * 60)
    print("🗑️  系统权限文件删除工具 - 完整版")
    print("=" * 60)
    print("专门解决Windows系统权限问题导致的文件无法删除")
    print()

    # 显示系统状态
    admin_status = "✅ 管理员模式" if is_admin() else "⚠️ 普通用户模式"
    psutil_status = "✅ 已安装" if PSUTIL_AVAILABLE else "❌ 未安装"
    gui_status = "✅ 可用" if GUI_AVAILABLE else "❌ 不可用"

    print(f"系统状态:")
    print(f"  权限状态: {admin_status}")
    print(f"  psutil库: {psutil_status}")
    print(f"  图形界面: {gui_status}")
    print()

    print("功能选择:")
    print("1. 单个文件/目录删除 (推荐)")
    print("2. 批量文件删除")
    print("3. 文件诊断分析")
    if GUI_AVAILABLE:
        print("4. 启动图形界面")
    print("5. 查看日志文件")
    print("6. 系统信息检查")
    print("0. 退出程序")
    print()

def single_file_delete():
    """单个文件删除模式"""
    deleter = SystemPermissionDeleter()

    print("\n🗑️ 单个文件/目录删除模式")
    print("-" * 40)

    # 获取路径
    if len(sys.argv) > 1:
        target_path = sys.argv[1].strip('"\'')
        print(f"从命令行参数获取路径: {target_path}")
    else:
        target_path = input("请输入要删除的文件或目录路径: ").strip('"\'')

    if not target_path:
        print("❌ 未提供路径")
        return

    if not os.path.exists(target_path):
        print(f"❌ 路径不存在: {target_path}")
        return

    print(f"\n🎯 目标路径: {target_path}")

    # 诊断文件
    print("\n🔍 正在诊断文件...")
    deleter.diagnose_file(target_path)

    # 确认删除
    confirm = input("\n⚠️ 确认强制删除？此操作不可撤销！(y/n): ").lower()
    if confirm != 'y':
        print("❌ 取消删除")
        return

    print("\n🚀 开始删除过程...")
    print("=" * 50)

    # 执行删除
    start_time = time.time()
    success = ultimate_force_delete(target_path)
    end_time = time.time()

    print("\n" + "=" * 50)
    if success:
        print("🎉 删除成功！")
        print(f"⏱️ 耗时: {end_time - start_time:.2f}秒")
        deleter.log(f"单个文件删除成功: {target_path}")
    else:
        print("❌ 删除失败")
        print("\n💡 建议:")
        print("   1. 重启计算机后重新运行")
        print("   2. 在安全模式下运行")
        print("   3. 检查是否有程序正在使用该文件")
        deleter.log(f"单个文件删除失败: {target_path}", "ERROR")

def batch_file_delete():
    """批量文件删除模式"""
    deleter = SystemPermissionDeleter()

    print("\n📁 批量文件删除模式")
    print("-" * 40)
    print("支持多个路径，每行一个，空行结束输入")
    print()

    paths = []
    print("请输入要删除的文件/目录路径（每行一个，空行结束）:")

    while True:
        path = input().strip('"\'')
        if not path:
            break
        if os.path.exists(path):
            paths.append(path)
            print(f"  ✅ 已添加: {path}")
        else:
            print(f"  ❌ 路径不存在: {path}")

    if not paths:
        print("❌ 没有有效的路径")
        return

    print(f"\n📊 共选择了 {len(paths)} 个项目")
    for i, path in enumerate(paths, 1):
        print(f"  {i}. {path}")

    # 确认删除
    confirm = input(f"\n⚠️ 确认删除这 {len(paths)} 个项目？此操作不可撤销！(y/n): ").lower()
    if confirm != 'y':
        print("❌ 取消删除")
        return

    print("\n🚀 开始批量删除...")
    print("=" * 60)

    # 执行批量删除
    deleter.selected_paths = paths
    success_count, failed_count, skipped_count = deleter.batch_delete(paths)

    # 显示结果
    total = len(paths)
    duration = deleter.deletion_stats['end_time'] - deleter.deletion_stats['start_time']

    print("\n" + "=" * 60)
    print("🎉 批量删除完成！")
    print(f"📊 统计结果:")
    print(f"  总计: {total} 项")
    print(f"  成功: {success_count} 项")
    print(f"  失败: {failed_count} 项")
    print(f"  跳过: {skipped_count} 项")
    print(f"  耗时: {str(duration).split('.')[0]}")
    print(f"📋 详细日志: {deleter.log_file_path}")

def file_diagnosis():
    """文件诊断模式"""
    deleter = SystemPermissionDeleter()

    print("\n🔍 文件诊断分析模式")
    print("-" * 40)

    target_path = input("请输入要诊断的文件或目录路径: ").strip('"\'')

    if not target_path:
        print("❌ 未提供路径")
        return

    if not os.path.exists(target_path):
        print(f"❌ 路径不存在: {target_path}")
        return

    print(f"\n🎯 诊断目标: {target_path}")
    print("=" * 50)

    # 执行诊断
    deleter.diagnose_file(target_path)

    print("\n✅ 诊断完成")

class SystemPermissionGUI:
    """系统权限删除工具图形界面"""

    def __init__(self, root):
        self.root = root
        self.root.title("系统权限文件删除工具 - 完整版")
        self.root.geometry("1000x800")
        self.root.configure(bg='#f0f0f0')

        # 初始化删除器
        self.deleter = SystemPermissionDeleter()

        # 初始化变量
        self.selected_paths = []

        self.setup_ui()

        # 记录启动信息
        self.log("🚀 图形界面启动完成")

    def setup_ui(self):
        """设置用户界面"""
        # 标题栏
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🗑️ 系统权限文件删除工具",
                              font=('微软雅黑', 18, 'bold'),
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)

        # 状态信息栏
        status_frame = tk.Frame(self.root, bg='#ecf0f1', height=60)
        status_frame.pack(fill='x', padx=5, pady=2)
        status_frame.pack_propagate(False)

        # 系统状态
        admin_status = "✅ 管理员模式" if self.deleter.is_admin else "⚠️ 普通用户模式"
        psutil_status = "✅ 已安装" if PSUTIL_AVAILABLE else "❌ 未安装"

        status_text = f"权限状态: {admin_status} | psutil库: {psutil_status}"
        status_label = tk.Label(status_frame, text=status_text,
                               font=('微软雅黑', 10), bg='#ecf0f1')
        status_label.pack(expand=True)

        # 创建主要内容区域
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # 左侧功能区
        left_frame = tk.Frame(main_frame, bg='#f0f0f0', width=300)
        left_frame.pack(side='left', fill='y', padx=5)
        left_frame.pack_propagate(False)

        # 右侧日志区
        right_frame = tk.Frame(main_frame, bg='#f0f0f0')
        right_frame.pack(side='right', fill='both', expand=True, padx=5)

        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)

    def setup_left_panel(self, parent):
        """设置左侧功能面板"""
        # 文件选择区域
        select_frame = tk.LabelFrame(parent, text="📁 文件选择",
                                    font=('微软雅黑', 11), bg='#f0f0f0')
        select_frame.pack(fill='x', pady=5)

        # 选择按钮
        btn_frame = tk.Frame(select_frame, bg='#f0f0f0')
        btn_frame.pack(fill='x', padx=5, pady=5)

        tk.Button(btn_frame, text="选择文件", command=self.select_files,
                 bg='#3498db', fg='white', font=('微软雅黑', 10), width=12).pack(side='left', padx=2)

        tk.Button(btn_frame, text="选择文件夹", command=self.select_folder,
                 bg='#9b59b6', fg='white', font=('微软雅黑', 10), width=12).pack(side='left', padx=2)

        tk.Button(btn_frame, text="清空列表", command=self.clear_list,
                 bg='#95a5a6', fg='white', font=('微软雅黑', 10), width=12).pack(side='left', padx=2)

        # 文件列表
        list_frame = tk.Frame(select_frame, bg='#f0f0f0')
        list_frame.pack(fill='both', expand=True, padx=5, pady=5)

        self.file_listbox = tk.Listbox(list_frame, font=('Consolas', 9), height=8)
        scrollbar1 = tk.Scrollbar(list_frame, orient='vertical', command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar1.set)

        self.file_listbox.pack(side='left', fill='both', expand=True)
        scrollbar1.pack(side='right', fill='y')

        # 删除选项
        options_frame = tk.LabelFrame(parent, text="🔧 删除选项",
                                     font=('微软雅黑', 11), bg='#f0f0f0')
        options_frame.pack(fill='x', pady=5)

        self.force_delete = tk.BooleanVar(value=True)
        self.backup_before_delete = tk.BooleanVar(value=False)
        self.kill_processes = tk.BooleanVar(value=True)

        tk.Checkbutton(options_frame, text="强制删除（获取所有权）",
                      variable=self.force_delete, font=('微软雅黑', 9),
                      bg='#f0f0f0').pack(anchor='w', padx=5)

        tk.Checkbutton(options_frame, text="删除前创建备份",
                      variable=self.backup_before_delete, font=('微软雅黑', 9),
                      bg='#f0f0f0').pack(anchor='w', padx=5)

        if PSUTIL_AVAILABLE:
            tk.Checkbutton(options_frame, text="自动结束占用进程",
                          variable=self.kill_processes, font=('微软雅黑', 9),
                          bg='#f0f0f0').pack(anchor='w', padx=5)

        # 操作按钮
        action_frame = tk.LabelFrame(parent, text="🚀 操作",
                                    font=('微软雅黑', 11), bg='#f0f0f0')
        action_frame.pack(fill='x', pady=5)

        # 第一行按钮
        btn_row1 = tk.Frame(action_frame, bg='#f0f0f0')
        btn_row1.pack(fill='x', padx=5, pady=5)

        self.diagnose_btn = tk.Button(btn_row1, text="🔍 诊断文件",
                                     command=self.diagnose_files,
                                     bg='#f39c12', fg='white',
                                     font=('微软雅黑', 10))
        self.diagnose_btn.pack(side='left', fill='x', expand=True, padx=2)

        self.delete_btn = tk.Button(btn_row1, text="🗑️ 开始删除",
                                   command=self.start_deletion,
                                   bg='#e74c3c', fg='white',
                                   font=('微软雅黑', 10, 'bold'))
        self.delete_btn.pack(side='right', fill='x', expand=True, padx=2)

        # 第二行按钮
        btn_row2 = tk.Frame(action_frame, bg='#f0f0f0')
        btn_row2.pack(fill='x', padx=5, pady=2)

        tk.Button(btn_row2, text="📋 查看日志", command=self.open_log_file,
                 bg='#3498db', fg='white', font=('微软雅黑', 9)).pack(side='left', fill='x', expand=True, padx=2)

        tk.Button(btn_row2, text="🔧 系统检查", command=self.system_check,
                 bg='#27ae60', fg='white', font=('微软雅黑', 9)).pack(side='right', fill='x', expand=True, padx=2)

        # 进度条
        self.progress = ttk.Progressbar(parent, mode='indeterminate')
        self.progress.pack(fill='x', pady=5)

    def setup_right_panel(self, parent):
        """设置右侧日志面板"""
        log_frame = tk.LabelFrame(parent, text="📋 操作日志",
                                 font=('微软雅黑', 11), bg='#f0f0f0')
        log_frame.pack(fill='both', expand=True)

        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=25,
                                                 font=('Consolas', 9),
                                                 bg='#2c3e50', fg='#ecf0f1')
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)

        # 日志控制按钮
        log_btn_frame = tk.Frame(log_frame, bg='#f0f0f0')
        log_btn_frame.pack(fill='x', padx=5, pady=5)

        tk.Button(log_btn_frame, text="清空日志", command=self.clear_log,
                 bg='#95a5a6', fg='white', font=('微软雅黑', 9)).pack(side='left', padx=5)

        tk.Button(log_btn_frame, text="保存日志", command=self.save_log,
                 bg='#34495e', fg='white', font=('微软雅黑', 9)).pack(side='left', padx=5)

    def log(self, message, level="INFO"):
        """添加日志信息到GUI和文件"""
        timestamp = time.strftime("%H:%M:%S")
        gui_message = f"[{timestamp}] {message}"

        # 根据级别设置颜色
        if level.upper() == "ERROR":
            color = "#e74c3c"
        elif level.upper() == "WARNING":
            color = "#f39c12"
        elif level.upper() == "SUCCESS":
            color = "#27ae60"
        else:
            color = "#ecf0f1"

        # 添加到GUI
        self.log_text.insert(tk.END, f"{gui_message}\n")

        # 设置颜色（简化版本，只设置最后一行）
        last_line = self.log_text.index(tk.END + "-2l")
        self.log_text.tag_add("current", last_line, tk.END + "-1c")
        self.log_text.tag_config("current", foreground=color)

        self.log_text.see(tk.END)
        self.root.update_idletasks()

        # 添加到文件日志
        self.deleter.log(message, level)

    def select_files(self):
        """选择文件"""
        files = filedialog.askopenfilenames(title="选择要删除的文件")
        for file in files:
            if file not in self.selected_paths:
                self.selected_paths.append(file)
                self.file_listbox.insert(tk.END, f"📄 {file}")

        if files:
            self.log(f"已添加 {len(files)} 个文件")

    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择要删除的文件夹")
        if folder and folder not in self.selected_paths:
            self.selected_paths.append(folder)
            self.file_listbox.insert(tk.END, f"📁 {folder}")
            self.log(f"已添加文件夹: {folder}")

    def clear_list(self):
        """清空选择列表"""
        self.selected_paths.clear()
        self.file_listbox.delete(0, tk.END)
        self.log("已清空文件列表")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志"""
        try:
            log_content = self.log_text.get(1.0, tk.END)
            filename = filedialog.asksaveasfilename(
                title="保存日志",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                self.log(f"日志已保存到: {filename}", "SUCCESS")
        except Exception as e:
            self.log(f"保存日志失败: {e}", "ERROR")

    def open_log_file(self):
        """打开日志文件"""
        try:
            os.startfile(self.deleter.log_file_path)
            self.log("已打开日志文件")
        except Exception as e:
            self.log(f"无法打开日志文件: {e}", "ERROR")

    def system_check(self):
        """系统检查"""
        self.log("🔧 开始系统检查...")

        # 权限检查
        admin_status = "是" if self.deleter.is_admin else "否"
        self.log(f"  管理员权限: {admin_status}")

        # 依赖检查
        psutil_status = "已安装" if PSUTIL_AVAILABLE else "未安装"
        self.log(f"  psutil库: {psutil_status}")

        # Python版本
        self.log(f"  Python版本: {sys.version.split()[0]}")

        # 目录检查
        for dir_name in ['logs', 'reports']:
            if os.path.exists(dir_name):
                file_count = len(os.listdir(dir_name))
                self.log(f"  {dir_name}目录: 存在 ({file_count} 个文件)")
            else:
                self.log(f"  {dir_name}目录: 不存在")

        self.log("✅ 系统检查完成")

    def diagnose_files(self):
        """诊断选中的文件"""
        if not self.selected_paths:
            messagebox.showwarning("警告", "请先选择要诊断的文件或文件夹！")
            return

        self.log("🔍 开始文件诊断...")

        for path in self.selected_paths:
            self.diagnose_single_file(path)

    def diagnose_single_file(self, path):
        """诊断单个文件"""
        self.log(f"📋 诊断: {path}")

        if not os.path.exists(path):
            self.log(f"  ❌ 文件不存在", "ERROR")
            return

        try:
            # 基本信息
            stat_info = os.stat(path)
            self.log(f"  📊 大小: {stat_info.st_size} 字节")
            self.log(f"  📅 修改时间: {datetime.fromtimestamp(stat_info.st_mtime)}")

            # 权限信息
            is_readonly = not (stat_info.st_mode & stat.S_IWRITE)
            self.log(f"  🔒 只读属性: {'是' if is_readonly else '否'}")

            # 检查进程占用
            if os.path.isfile(path) and PSUTIL_AVAILABLE:
                processes = self.deleter.get_processes_using_file(path)
                if processes:
                    self.log(f"  ⚠️ 被 {len(processes)} 个进程占用:", "WARNING")
                    for proc in processes:
                        self.log(f"    - {proc['name']} (PID: {proc['pid']})")
                else:
                    self.log(f"  ✅ 未被占用")
            elif not PSUTIL_AVAILABLE:
                self.log(f"  ℹ️ 进程检测不可用（需要psutil）")

            # 父目录权限
            parent_dir = os.path.dirname(path)
            if os.path.exists(parent_dir):
                parent_stat = os.stat(parent_dir)
                parent_readonly = not (parent_stat.st_mode & stat.S_IWRITE)
                self.log(f"  📁 父目录只读: {'是' if parent_readonly else '否'}")

        except Exception as e:
            self.log(f"  ❌ 诊断失败: {str(e)}", "ERROR")

    def start_deletion(self):
        """开始删除操作"""
        if not self.selected_paths:
            messagebox.showwarning("警告", "请先选择要删除的文件或文件夹！")
            return

        # 确认删除
        count = len(self.selected_paths)
        if not messagebox.askyesno("确认删除",
                                  f"确定要删除选中的 {count} 个项目吗？\n\n"
                                  "此操作不可撤销！"):
            return

        # 禁用删除按钮
        self.delete_btn.config(state='disabled')
        self.progress.start()

        # 在新线程中执行删除
        import threading
        threading.Thread(target=self.delete_files_thread, daemon=True).start()

    def delete_files_thread(self):
        """删除文件的线程函数"""
        try:
            success_count = 0
            failed_count = 0
            skipped_count = 0

            self.log("🚀 开始删除操作...")

            for i, path in enumerate(self.selected_paths, 1):
                self.log(f"\n[{i}/{len(self.selected_paths)}] 处理: {path}")

                if not os.path.exists(path):
                    self.log(f"  ⚠️ 路径不存在，跳过", "WARNING")
                    skipped_count += 1
                    continue

                # 诊断文件
                if self.kill_processes.get() and PSUTIL_AVAILABLE:
                    self.deleter.kill_processes_using_file(path)

                # 备份（如果选择）
                if self.backup_before_delete.get():
                    self.create_backup(path)

                # 执行删除
                if self.force_delete.get():
                    success = ultimate_force_delete(path)
                else:
                    success = self.simple_delete(path)

                if success:
                    self.log(f"  ✅ 删除成功", "SUCCESS")
                    success_count += 1
                else:
                    self.log(f"  ❌ 删除失败", "ERROR")
                    failed_count += 1

            # 完成后的处理
            self.root.after(0, self.deletion_completed, success_count, failed_count, skipped_count)

        except Exception as e:
            self.log(f"删除过程异常: {e}", "ERROR")
            self.root.after(0, self.deletion_completed, 0, len(self.selected_paths), 0)

    def simple_delete(self, path):
        """简单删除方法"""
        try:
            if os.path.isfile(path):
                os.remove(path)
            else:
                shutil.rmtree(path)
            return not os.path.exists(path)
        except Exception as e:
            self.log(f"    简单删除失败: {e}")
            return False

    def create_backup(self, path):
        """创建备份"""
        try:
            backup_dir = os.path.join(os.path.dirname(path), "backup_" +
                                    time.strftime("%Y%m%d_%H%M%S"))
            os.makedirs(backup_dir, exist_ok=True)

            backup_path = os.path.join(backup_dir, os.path.basename(path))

            if os.path.isfile(path):
                shutil.copy2(path, backup_path)
            else:
                shutil.copytree(path, backup_path)

            self.log(f"  📦 已创建备份: {backup_path}")
        except Exception as e:
            self.log(f"  ⚠️ 备份失败: {str(e)}", "WARNING")

    def deletion_completed(self, success_count, failed_count, skipped_count):
        """删除完成后的处理"""
        self.progress.stop()
        self.delete_btn.config(state='normal')

        total = success_count + failed_count + skipped_count

        self.log(f"\n🎉 删除操作完成！")
        self.log(f"📊 统计结果:")
        self.log(f"  总计: {total} 项")
        self.log(f"  成功: {success_count} 项", "SUCCESS")
        self.log(f"  失败: {failed_count} 项", "ERROR" if failed_count > 0 else "INFO")
        self.log(f"  跳过: {skipped_count} 项", "WARNING" if skipped_count > 0 else "INFO")

        # 显示结果对话框
        if failed_count == 0 and skipped_count == 0:
            messagebox.showinfo("完成", f"🎉 所有 {success_count} 个项目删除成功！")
        elif failed_count == 0:
            messagebox.showinfo("完成", f"✅ 删除完成！\n成功: {success_count} 项\n跳过: {skipped_count} 项")
        else:
            messagebox.showwarning("部分完成",
                                 f"删除完成：\n"
                                 f"✅ 成功: {success_count} 项\n"
                                 f"❌ 失败: {failed_count} 项\n"
                                 f"⚠️ 跳过: {skipped_count} 项\n\n"
                                 f"请查看日志了解详情")

def launch_gui():
    """启动图形界面"""
    if not GUI_AVAILABLE:
        print("❌ 图形界面不可用，缺少tkinter模块")
        print("请安装tkinter或使用命令行模式")
        return False

    try:
        root = tk.Tk()
        app = SystemPermissionGUI(root)

        # 设置窗口图标
        try:
            if os.path.exists('icon.ico'):
                root.iconbitmap('icon.ico')
                app.log("✅ 已加载自定义图标")
            else:
                app.log("ℹ️ 未找到图标文件，使用默认图标")
        except Exception as e:
            app.log(f"⚠️ 图标加载失败: {e}", "WARNING")

        # 居中显示窗口
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f'{width}x{height}+{x}+{y}')

        # 设置关闭事件
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出程序吗？"):
                root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        root.mainloop()
        return True

    except Exception as e:
        print(f"❌ 启动图形界面失败: {e}")
        return False

def show_logs():
    """显示日志"""
    print("\n📋 日志文件查看")
    print("-" * 40)

    log_dir = "logs"
    if not os.path.exists(log_dir):
        print("❌ 日志目录不存在")
        return

    log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]

    if not log_files:
        print("❌ 没有找到日志文件")
        return

    print("可用的日志文件:")
    for i, log_file in enumerate(log_files, 1):
        file_path = os.path.join(log_dir, log_file)
        file_size = os.path.getsize(file_path)
        print(f"  {i}. {log_file} ({file_size} 字节)")

    try:
        choice = int(input(f"\n请选择要查看的日志文件 (1-{len(log_files)}): "))
        if 1 <= choice <= len(log_files):
            log_file = os.path.join(log_dir, log_files[choice-1])

            print(f"\n📄 日志文件: {log_file}")
            print("-" * 60)

            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 显示最后50行
            if len(lines) > 50:
                print(f"显示最后50行 (共{len(lines)}行):")
                lines = lines[-50:]

            for line in lines:
                print(line.rstrip())

        else:
            print("❌ 无效选择")

    except ValueError:
        print("❌ 请输入有效数字")
    except Exception as e:
        print(f"❌ 读取日志失败: {e}")

def system_check():
    """系统信息检查"""
    print("\n🔧 系统信息检查")
    print("-" * 40)

    print("Python环境:")
    print(f"  版本: {sys.version}")
    print(f"  平台: {sys.platform}")
    print()

    print("权限状态:")
    admin_status = is_admin()
    print(f"  管理员权限: {'✅ 是' if admin_status else '❌ 否'}")

    if not admin_status:
        print("  💡 建议以管理员身份运行以获得最佳效果")
    print()

    print("依赖库状态:")
    print(f"  psutil: {'✅ 已安装' if PSUTIL_AVAILABLE else '❌ 未安装'}")
    print(f"  tkinter: {'✅ 可用' if GUI_AVAILABLE else '❌ 不可用'}")

    if not PSUTIL_AVAILABLE:
        print("  💡 安装psutil可获得进程检测功能: pip install psutil")
    print()

    print("工具文件状态:")
    current_dir = os.getcwd()
    print(f"  当前目录: {current_dir}")

    # 检查目录结构
    dirs_to_check = ['logs', 'reports']
    for dir_name in dirs_to_check:
        if os.path.exists(dir_name):
            file_count = len(os.listdir(dir_name))
            print(f"  {dir_name}/: ✅ 存在 ({file_count} 个文件)")
        else:
            print(f"  {dir_name}/: ❌ 不存在")

def main():
    """主函数 - 直接启动图形界面"""

    # 如果有命令行参数，进入命令行模式
    if len(sys.argv) > 1:
        # 检查是否是帮助参数
        if sys.argv[1] in ['-h', '--help', '/?']:
            print("系统权限文件删除工具 - 使用说明")
            print("=" * 50)
            print("用法:")
            print("  python 系统权限删除工具.py                    # 启动图形界面")
            print("  python 系统权限删除工具.py <文件路径>         # 命令行删除")
            print("  python 系统权限删除工具.py --cli             # 强制命令行模式")
            print()
            print("功能:")
            print("  - 解决Windows系统权限删除问题")
            print("  - 支持单个和批量文件删除")
            print("  - 详细的文件诊断功能")
            print("  - 完整的操作日志记录")
            return

        # 检查是否强制命令行模式
        if sys.argv[1] == '--cli':
            command_line_mode()
            return

        # 直接删除指定文件
        single_file_delete()
        return

    # 检查图形界面可用性
    if not GUI_AVAILABLE:
        print("❌ 图形界面不可用，缺少tkinter模块")
        print("自动切换到命令行模式...")
        command_line_mode()
        return

    # 检查管理员权限（静默检查，不强制）
    if not is_admin():
        # 在图形界面中会显示权限状态，这里不需要强制提升
        pass

    # 启动图形界面
    print("🚀 启动图形界面...")
    success = launch_gui()

    if not success:
        print("图形界面启动失败，切换到命令行模式...")
        command_line_mode()

def command_line_mode():
    """命令行模式"""
    print("\n" + "=" * 60)
    print("🗑️  系统权限文件删除工具 - 命令行模式")
    print("=" * 60)

    # 检查管理员权限
    if not is_admin():
        print("⚠️ 建议以管理员身份运行以获得最佳效果")
        choice = input("是否以管理员身份重新运行？(y/n): ").lower()
        if choice == 'y':
            if not run_as_admin():
                sys.exit(1)
            return

    # 主循环
    while True:
        show_main_menu()

        try:
            choice = input("请选择功能 (0-6): ").strip()

            if choice == '0':
                print("\n👋 再见！")
                break
            elif choice == '1':
                single_file_delete()
            elif choice == '2':
                batch_file_delete()
            elif choice == '3':
                file_diagnosis()
            elif choice == '4' and GUI_AVAILABLE:
                print("启动图形界面...")
                if launch_gui():
                    break  # 图形界面启动成功，退出命令行模式
            elif choice == '5':
                show_logs()
            elif choice == '6':
                system_check()
            else:
                print("❌ 无效选择，请重新输入")

            input("\n按回车键继续...")

        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
