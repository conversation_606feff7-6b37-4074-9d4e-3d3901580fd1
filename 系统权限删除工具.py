#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统权限文件删除工具 - 完整版
专门解决Windows系统权限问题导致的文件无法删除
整合了图形界面、命令行、批量处理等所有功能
"""

import os
import sys
import subprocess
import time
import shutil
import stat
import ctypes
import logging
import json
import tempfile
from pathlib import Path
from datetime import datetime
import traceback

# 尝试导入GUI相关模块
try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

# 尝试导入psutil
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

def is_admin():
    """检查是否以管理员身份运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员身份重新运行"""
    if is_admin():
        return True
    else:
        print("⚠️ 需要管理员权限，正在尝试提升权限...")
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            print("❌ 无法获取管理员权限")
            return False

def take_ownership(path):
    """获取文件/目录的所有权"""
    print(f"🔑 正在获取所有权: {path}")
    
    try:
        # 方法1: 使用takeown命令
        takeown_cmd = f'takeown /f "{path}" /r /d y'
        result1 = subprocess.run(takeown_cmd, shell=True, capture_output=True, text=True)
        
        if result1.returncode == 0:
            print("  ✅ takeown 成功")
        else:
            print(f"  ⚠️ takeown 部分成功: {result1.stderr.strip()}")
        
        # 方法2: 给当前用户完全控制权限
        username = os.environ.get('USERNAME', 'Administrator')
        icacls_cmd = f'icacls "{path}" /grant "{username}":F /t /c /q'
        result2 = subprocess.run(icacls_cmd, shell=True, capture_output=True, text=True)
        
        if result2.returncode == 0:
            print("  ✅ 用户权限设置成功")
        else:
            print(f"  ⚠️ 用户权限设置部分成功")
        
        # 方法3: 给Administrators组完全控制权限
        admin_cmd = f'icacls "{path}" /grant Administrators:F /t /c /q'
        result3 = subprocess.run(admin_cmd, shell=True, capture_output=True, text=True)
        
        # 方法4: 给SYSTEM完全控制权限
        system_cmd = f'icacls "{path}" /grant SYSTEM:F /t /c /q'
        result4 = subprocess.run(system_cmd, shell=True, capture_output=True, text=True)
        
        # 方法5: 移除继承并重新设置权限
        reset_cmd = f'icacls "{path}" /inheritance:r /grant "{username}":F /t /c /q'
        result5 = subprocess.run(reset_cmd, shell=True, capture_output=True, text=True)
        
        print("  ✅ 权限设置完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 获取所有权失败: {e}")
        return False

def remove_readonly_recursive(path):
    """递归移除只读属性"""
    print(f"🔓 移除只读属性: {path}")
    
    try:
        if os.path.isfile(path):
            # 单个文件
            current_attr = os.stat(path).st_mode
            os.chmod(path, current_attr | stat.S_IWRITE)
        else:
            # 目录
            for root, dirs, files in os.walk(path):
                # 处理文件
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        current_attr = os.stat(file_path).st_mode
                        os.chmod(file_path, current_attr | stat.S_IWRITE)
                    except Exception as e:
                        print(f"    ⚠️ 无法修改文件属性: {file_path} - {e}")
                
                # 处理目录
                for dir in dirs:
                    dir_path = os.path.join(root, dir)
                    try:
                        current_attr = os.stat(dir_path).st_mode
                        os.chmod(dir_path, current_attr | stat.S_IWRITE)
                    except Exception as e:
                        print(f"    ⚠️ 无法修改目录属性: {dir_path} - {e}")
        
        print("  ✅ 只读属性移除完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 移除只读属性失败: {e}")
        return False

def force_delete_with_system_commands(path):
    """使用系统命令强制删除"""
    print(f"💻 使用系统命令删除: {path}")
    
    methods = [
        ("rmdir /s /q", f'rmdir /s /q "{path}"'),
        ("del /f /s /q", f'del /f /s /q "{path}\\*" && rmdir /s /q "{path}"'),
        ("PowerShell Remove-Item", f'powershell -Command "Remove-Item -Path \\"{path}\\" -Recurse -Force -ErrorAction SilentlyContinue"'),
        ("rd /s /q", f'rd /s /q "{path}"'),
    ]
    
    for method_name, cmd in methods:
        try:
            print(f"  🔄 尝试: {method_name}")
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if not os.path.exists(path):
                print(f"  ✅ {method_name} 成功")
                return True
            else:
                print(f"  ❌ {method_name} 失败")
                
        except Exception as e:
            print(f"  ❌ {method_name} 异常: {e}")
    
    return False

def robocopy_delete(path):
    """使用robocopy清空目录"""
    if not os.path.isdir(path):
        return False
    
    print(f"🔄 使用robocopy清空: {path}")
    
    try:
        import tempfile
        
        # 创建空目录
        empty_dir = tempfile.mkdtemp()
        
        # 使用robocopy镜像空目录到目标目录
        robocopy_cmd = f'robocopy "{empty_dir}" "{path}" /mir /r:0 /w:0 /np /nfl /ndl'
        result = subprocess.run(robocopy_cmd, shell=True, capture_output=True, text=True)
        
        # 清理空目录
        try:
            os.rmdir(empty_dir)
        except:
            pass
        
        # 删除现在为空的目录
        try:
            os.rmdir(path)
            if not os.path.exists(path):
                print("  ✅ robocopy删除成功")
                return True
        except:
            pass
        
        print("  ❌ robocopy删除失败")
        return False
        
    except Exception as e:
        print(f"  ❌ robocopy删除异常: {e}")
        return False

def ultimate_force_delete(path):
    """终极强制删除"""
    if not os.path.exists(path):
        print(f"❌ 路径不存在: {path}")
        return False
    
    print(f"🗑️ 开始终极强制删除: {path}")
    print("=" * 60)
    
    # 步骤1: 获取所有权
    take_ownership(path)
    time.sleep(1)
    
    # 步骤2: 移除只读属性
    remove_readonly_recursive(path)
    time.sleep(1)
    
    # 步骤3: 尝试Python标准删除
    print("🐍 尝试Python标准删除...")
    try:
        if os.path.isfile(path):
            os.remove(path)
        else:
            shutil.rmtree(path, ignore_errors=True)
        
        if not os.path.exists(path):
            print("  ✅ Python删除成功")
            return True
        else:
            print("  ❌ Python删除失败")
    except Exception as e:
        print(f"  ❌ Python删除异常: {e}")
    
    # 步骤4: 使用系统命令
    if force_delete_with_system_commands(path):
        return True
    
    # 步骤5: 使用robocopy（仅对目录）
    if os.path.isdir(path):
        if robocopy_delete(path):
            return True
    
    # 步骤6: 移动到临时目录
    print("📁 尝试移动到临时目录...")
    try:
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_name = f"deleted_{int(time.time())}_{os.path.basename(path)}"
        temp_path = os.path.join(temp_dir, temp_name)
        
        shutil.move(path, temp_path)
        print(f"  ✅ 已移动到: {temp_path}")
        print("  ℹ️ 重启后可手动删除临时文件")
        return True
        
    except Exception as e:
        print(f"  ❌ 移动失败: {e}")
    
    print("❌ 所有删除方法都失败了")
    return False

class SystemPermissionDeleter:
    """系统权限删除工具类 - 整合所有功能"""

    def __init__(self):
        self.setup_logging()
        self.is_admin = is_admin()
        self.selected_paths = []
        self.deletion_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'start_time': None,
            'end_time': None
        }

    def setup_logging(self):
        """设置日志系统"""
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_filename = os.path.join(log_dir, f"system_deletion_log_{datetime.now().strftime('%Y%m%d')}.log")

        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

        self.logger = logging.getLogger('SystemDeleter')
        self.log_file_path = log_filename

    def log(self, message, level="INFO"):
        """记录日志"""
        print(message)
        if level.upper() == "ERROR":
            self.logger.error(message)
        elif level.upper() == "WARNING":
            self.logger.warning(message)
        elif level.upper() == "DEBUG":
            self.logger.debug(message)
        else:
            self.logger.info(message)

    def get_processes_using_file(self, filepath):
        """获取正在使用文件的进程列表"""
        processes = []

        if not PSUTIL_AVAILABLE:
            return processes

        try:
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    for item in proc.open_files():
                        if item.path.lower() == filepath.lower():
                            processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name']
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self.logger.debug(f"获取进程信息失败: {e}")

        return processes

    def kill_processes_using_file(self, filepath):
        """结束占用文件的进程"""
        if not PSUTIL_AVAILABLE:
            return False

        processes = self.get_processes_using_file(filepath)
        if not processes:
            return True

        self.log(f"  🔄 发现 {len(processes)} 个进程占用文件，尝试结束...")

        killed_count = 0
        for proc in processes:
            try:
                p = psutil.Process(proc['pid'])
                p.terminate()
                self.log(f"    ✅ 已终止: {proc['name']} (PID: {proc['pid']})")
                killed_count += 1
            except Exception as e:
                self.log(f"    ❌ 无法终止: {proc['name']} (PID: {proc['pid']}) - {e}")

        if killed_count > 0:
            time.sleep(2)  # 等待进程完全结束
            return True

        return False

    def diagnose_file(self, path):
        """诊断文件详细信息"""
        self.log(f"🔍 诊断文件: {path}")

        if not os.path.exists(path):
            self.log(f"  ❌ 文件不存在", "ERROR")
            return False

        try:
            # 基本信息
            stat_info = os.stat(path)
            self.log(f"  📊 大小: {stat_info.st_size} 字节")
            self.log(f"  📅 修改时间: {datetime.fromtimestamp(stat_info.st_mtime)}")

            # 权限信息
            is_readonly = not (stat_info.st_mode & stat.S_IWRITE)
            self.log(f"  🔒 只读属性: {'是' if is_readonly else '否'}")

            # 检查进程占用
            if os.path.isfile(path) and PSUTIL_AVAILABLE:
                processes = self.get_processes_using_file(path)
                if processes:
                    self.log(f"  ⚠️ 文件被 {len(processes)} 个进程占用:")
                    for proc in processes:
                        self.log(f"    - {proc['name']} (PID: {proc['pid']})")
                else:
                    self.log(f"  ✅ 文件未被占用")

            # 检查父目录权限
            parent_dir = os.path.dirname(path)
            if os.path.exists(parent_dir):
                parent_stat = os.stat(parent_dir)
                parent_readonly = not (parent_stat.st_mode & stat.S_IWRITE)
                self.log(f"  📁 父目录只读: {'是' if parent_readonly else '否'}")

            return True

        except Exception as e:
            self.log(f"  ❌ 诊断失败: {str(e)}", "ERROR")
            return False

    def batch_delete(self, paths):
        """批量删除文件"""
        self.deletion_stats['start_time'] = datetime.now()
        self.deletion_stats['total'] = len(paths)

        self.log(f"🚀 开始批量删除，共 {len(paths)} 个项目")

        success_count = 0
        failed_count = 0
        skipped_count = 0

        for i, path in enumerate(paths, 1):
            self.log(f"\n[{i}/{len(paths)}] 处理: {path}")

            if not os.path.exists(path):
                self.log(f"  ⚠️ 路径不存在，跳过", "WARNING")
                skipped_count += 1
                continue

            # 诊断文件
            self.diagnose_file(path)

            # 执行删除
            if ultimate_force_delete(path):
                self.log(f"  ✅ 删除成功")
                success_count += 1
            else:
                self.log(f"  ❌ 删除失败", "ERROR")
                failed_count += 1

        # 更新统计
        self.deletion_stats['success'] = success_count
        self.deletion_stats['failed'] = failed_count
        self.deletion_stats['skipped'] = skipped_count
        self.deletion_stats['end_time'] = datetime.now()

        # 生成报告
        self.generate_report()

        return success_count, failed_count, skipped_count

    def generate_report(self):
        """生成删除报告"""
        try:
            report_dir = "reports"
            if not os.path.exists(report_dir):
                os.makedirs(report_dir)

            report_file = os.path.join(report_dir, f"system_deletion_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

            stats_copy = self.deletion_stats.copy()
            if stats_copy.get('start_time'):
                stats_copy['start_time'] = stats_copy['start_time'].isoformat()
            if stats_copy.get('end_time'):
                stats_copy['end_time'] = stats_copy['end_time'].isoformat()

            report_data = {
                "timestamp": datetime.now().isoformat(),
                "statistics": stats_copy,
                "files_processed": self.selected_paths,
                "system_info": {
                    "admin_mode": self.is_admin,
                    "python_version": sys.version,
                    "platform": sys.platform,
                    "psutil_available": PSUTIL_AVAILABLE,
                    "gui_available": GUI_AVAILABLE
                }
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.log(f"📋 报告已保存: {report_file}")

        except Exception as e:
            self.log(f"❌ 生成报告失败: {e}", "ERROR")

def show_main_menu():
    """显示主菜单"""
    print("\n" + "=" * 60)
    print("🗑️  系统权限文件删除工具 - 完整版")
    print("=" * 60)
    print("专门解决Windows系统权限问题导致的文件无法删除")
    print()

    # 显示系统状态
    admin_status = "✅ 管理员模式" if is_admin() else "⚠️ 普通用户模式"
    psutil_status = "✅ 已安装" if PSUTIL_AVAILABLE else "❌ 未安装"
    gui_status = "✅ 可用" if GUI_AVAILABLE else "❌ 不可用"

    print(f"系统状态:")
    print(f"  权限状态: {admin_status}")
    print(f"  psutil库: {psutil_status}")
    print(f"  图形界面: {gui_status}")
    print()

    print("功能选择:")
    print("1. 单个文件/目录删除 (推荐)")
    print("2. 批量文件删除")
    print("3. 文件诊断分析")
    if GUI_AVAILABLE:
        print("4. 启动图形界面")
    print("5. 查看日志文件")
    print("6. 系统信息检查")
    print("0. 退出程序")
    print()

def single_file_delete():
    """单个文件删除模式"""
    deleter = SystemPermissionDeleter()

    print("\n🗑️ 单个文件/目录删除模式")
    print("-" * 40)

    # 获取路径
    if len(sys.argv) > 1:
        target_path = sys.argv[1].strip('"\'')
        print(f"从命令行参数获取路径: {target_path}")
    else:
        target_path = input("请输入要删除的文件或目录路径: ").strip('"\'')

    if not target_path:
        print("❌ 未提供路径")
        return

    if not os.path.exists(target_path):
        print(f"❌ 路径不存在: {target_path}")
        return

    print(f"\n🎯 目标路径: {target_path}")

    # 诊断文件
    print("\n🔍 正在诊断文件...")
    deleter.diagnose_file(target_path)

    # 确认删除
    confirm = input("\n⚠️ 确认强制删除？此操作不可撤销！(y/n): ").lower()
    if confirm != 'y':
        print("❌ 取消删除")
        return

    print("\n🚀 开始删除过程...")
    print("=" * 50)

    # 执行删除
    start_time = time.time()
    success = ultimate_force_delete(target_path)
    end_time = time.time()

    print("\n" + "=" * 50)
    if success:
        print("🎉 删除成功！")
        print(f"⏱️ 耗时: {end_time - start_time:.2f}秒")
        deleter.log(f"单个文件删除成功: {target_path}")
    else:
        print("❌ 删除失败")
        print("\n💡 建议:")
        print("   1. 重启计算机后重新运行")
        print("   2. 在安全模式下运行")
        print("   3. 检查是否有程序正在使用该文件")
        deleter.log(f"单个文件删除失败: {target_path}", "ERROR")

def batch_file_delete():
    """批量文件删除模式"""
    deleter = SystemPermissionDeleter()

    print("\n📁 批量文件删除模式")
    print("-" * 40)
    print("支持多个路径，每行一个，空行结束输入")
    print()

    paths = []
    print("请输入要删除的文件/目录路径（每行一个，空行结束）:")

    while True:
        path = input().strip('"\'')
        if not path:
            break
        if os.path.exists(path):
            paths.append(path)
            print(f"  ✅ 已添加: {path}")
        else:
            print(f"  ❌ 路径不存在: {path}")

    if not paths:
        print("❌ 没有有效的路径")
        return

    print(f"\n📊 共选择了 {len(paths)} 个项目")
    for i, path in enumerate(paths, 1):
        print(f"  {i}. {path}")

    # 确认删除
    confirm = input(f"\n⚠️ 确认删除这 {len(paths)} 个项目？此操作不可撤销！(y/n): ").lower()
    if confirm != 'y':
        print("❌ 取消删除")
        return

    print("\n🚀 开始批量删除...")
    print("=" * 60)

    # 执行批量删除
    deleter.selected_paths = paths
    success_count, failed_count, skipped_count = deleter.batch_delete(paths)

    # 显示结果
    total = len(paths)
    duration = deleter.deletion_stats['end_time'] - deleter.deletion_stats['start_time']

    print("\n" + "=" * 60)
    print("🎉 批量删除完成！")
    print(f"📊 统计结果:")
    print(f"  总计: {total} 项")
    print(f"  成功: {success_count} 项")
    print(f"  失败: {failed_count} 项")
    print(f"  跳过: {skipped_count} 项")
    print(f"  耗时: {str(duration).split('.')[0]}")
    print(f"📋 详细日志: {deleter.log_file_path}")

def file_diagnosis():
    """文件诊断模式"""
    deleter = SystemPermissionDeleter()

    print("\n🔍 文件诊断分析模式")
    print("-" * 40)

    target_path = input("请输入要诊断的文件或目录路径: ").strip('"\'')

    if not target_path:
        print("❌ 未提供路径")
        return

    if not os.path.exists(target_path):
        print(f"❌ 路径不存在: {target_path}")
        return

    print(f"\n🎯 诊断目标: {target_path}")
    print("=" * 50)

    # 执行诊断
    deleter.diagnose_file(target_path)

    print("\n✅ 诊断完成")

def launch_gui():
    """启动图形界面"""
    if not GUI_AVAILABLE:
        print("❌ 图形界面不可用，缺少tkinter模块")
        return

    print("\n🖥️ 启动图形界面...")

    try:
        # 这里可以集成之前的GUI代码
        print("⚠️ 图形界面功能正在开发中...")
        print("建议使用命令行模式获得完整功能")
    except Exception as e:
        print(f"❌ 启动图形界面失败: {e}")

def show_logs():
    """显示日志"""
    print("\n📋 日志文件查看")
    print("-" * 40)

    log_dir = "logs"
    if not os.path.exists(log_dir):
        print("❌ 日志目录不存在")
        return

    log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]

    if not log_files:
        print("❌ 没有找到日志文件")
        return

    print("可用的日志文件:")
    for i, log_file in enumerate(log_files, 1):
        file_path = os.path.join(log_dir, log_file)
        file_size = os.path.getsize(file_path)
        print(f"  {i}. {log_file} ({file_size} 字节)")

    try:
        choice = int(input(f"\n请选择要查看的日志文件 (1-{len(log_files)}): "))
        if 1 <= choice <= len(log_files):
            log_file = os.path.join(log_dir, log_files[choice-1])

            print(f"\n📄 日志文件: {log_file}")
            print("-" * 60)

            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 显示最后50行
            if len(lines) > 50:
                print(f"显示最后50行 (共{len(lines)}行):")
                lines = lines[-50:]

            for line in lines:
                print(line.rstrip())

        else:
            print("❌ 无效选择")

    except ValueError:
        print("❌ 请输入有效数字")
    except Exception as e:
        print(f"❌ 读取日志失败: {e}")

def system_check():
    """系统信息检查"""
    print("\n🔧 系统信息检查")
    print("-" * 40)

    print("Python环境:")
    print(f"  版本: {sys.version}")
    print(f"  平台: {sys.platform}")
    print()

    print("权限状态:")
    admin_status = is_admin()
    print(f"  管理员权限: {'✅ 是' if admin_status else '❌ 否'}")

    if not admin_status:
        print("  💡 建议以管理员身份运行以获得最佳效果")
    print()

    print("依赖库状态:")
    print(f"  psutil: {'✅ 已安装' if PSUTIL_AVAILABLE else '❌ 未安装'}")
    print(f"  tkinter: {'✅ 可用' if GUI_AVAILABLE else '❌ 不可用'}")

    if not PSUTIL_AVAILABLE:
        print("  💡 安装psutil可获得进程检测功能: pip install psutil")
    print()

    print("工具文件状态:")
    current_dir = os.getcwd()
    print(f"  当前目录: {current_dir}")

    # 检查目录结构
    dirs_to_check = ['logs', 'reports']
    for dir_name in dirs_to_check:
        if os.path.exists(dir_name):
            file_count = len(os.listdir(dir_name))
            print(f"  {dir_name}/: ✅ 存在 ({file_count} 个文件)")
        else:
            print(f"  {dir_name}/: ❌ 不存在")

def main():
    """主函数"""
    # 如果有命令行参数，直接进入单文件删除模式
    if len(sys.argv) > 1:
        single_file_delete()
        return

    # 检查管理员权限
    if not is_admin():
        print("⚠️ 建议以管理员身份运行以获得最佳效果")
        choice = input("是否以管理员身份重新运行？(y/n): ").lower()
        if choice == 'y':
            if not run_as_admin():
                sys.exit(1)
            return

    # 主循环
    while True:
        show_main_menu()

        try:
            choice = input("请选择功能 (0-6): ").strip()

            if choice == '0':
                print("\n👋 再见！")
                break
            elif choice == '1':
                single_file_delete()
            elif choice == '2':
                batch_file_delete()
            elif choice == '3':
                file_diagnosis()
            elif choice == '4' and GUI_AVAILABLE:
                launch_gui()
            elif choice == '5':
                show_logs()
            elif choice == '6':
                system_check()
            else:
                print("❌ 无效选择，请重新输入")

            input("\n按回车键继续...")

        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
