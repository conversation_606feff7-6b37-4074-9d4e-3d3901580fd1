#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版强制删除工具
快速解决Windows文件权限问题
"""

import os
import sys
import stat
import shutil
import subprocess
import ctypes
from pathlib import Path

def is_admin():
    """检查是否以管理员身份运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员身份重新运行"""
    if is_admin():
        return True
    else:
        print("⚠️ 需要管理员权限，正在尝试提升权限...")
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            print("❌ 无法获取管理员权限")
            return False

def remove_readonly(func, path, excinfo):
    """移除只读属性的回调函数"""
    try:
        os.chmod(path, stat.S_IWRITE)
        func(path)
    except:
        pass

def force_delete_file(file_path):
    """强制删除文件"""
    try:
        # 方法1: 直接删除
        if os.path.exists(file_path):
            os.chmod(file_path, stat.S_IWRITE)
            os.remove(file_path)
            return True
    except:
        pass
    
    try:
        # 方法2: 使用系统命令
        subprocess.run(f'del /f /q "{file_path}"', shell=True, check=True)
        return True
    except:
        pass
    
    return False

def force_delete_directory(dir_path):
    """强制删除目录"""
    try:
        # 方法1: 使用shutil
        shutil.rmtree(dir_path, onerror=remove_readonly)
        return True
    except:
        pass
    
    try:
        # 方法2: 使用系统命令
        subprocess.run(f'rmdir /s /q "{dir_path}"', shell=True, check=True)
        return True
    except:
        pass
    
    return False

def force_delete(path):
    """强制删除文件或目录"""
    if not os.path.exists(path):
        print(f"❌ 路径不存在: {path}")
        return False
    
    print(f"🗑️ 正在删除: {path}")
    
    if os.path.isfile(path):
        success = force_delete_file(path)
    else:
        success = force_delete_directory(path)
    
    if success:
        print(f"✅ 删除成功: {path}")
        return True
    else:
        print(f"❌ 删除失败: {path}")
        return False

def main():
    """主函数"""
    print("🗑️ Windows强制删除工具")
    print("=" * 50)
    
    # 检查管理员权限
    if not is_admin():
        print("⚠️ 建议以管理员身份运行以获得最佳效果")
        choice = input("是否以管理员身份重新运行？(y/n): ").lower()
        if choice == 'y':
            if not run_as_admin():
                sys.exit(1)
            return
    else:
        print("✅ 管理员权限已获取")
    
    print("\n使用方法:")
    print("1. 输入文件或文件夹的完整路径")
    print("2. 输入 'quit' 或 'exit' 退出程序")
    print("3. 支持拖拽文件到命令行窗口")
    print("-" * 50)
    
    while True:
        try:
            path = input("\n请输入要删除的路径: ").strip()
            
            if path.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            if not path:
                continue
            
            # 处理拖拽文件的引号
            path = path.strip('"\'')
            
            # 确认删除
            print(f"\n⚠️ 即将删除: {path}")
            confirm = input("确认删除？(y/n): ").lower()
            
            if confirm == 'y':
                force_delete(path)
            else:
                print("❌ 取消删除")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
