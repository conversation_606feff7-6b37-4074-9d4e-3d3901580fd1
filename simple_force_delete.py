#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版强制删除工具
快速解决Windows文件权限问题
"""

import os
import sys
import stat
import shutil
import subprocess
import ctypes
import logging
import time
from pathlib import Path
from datetime import datetime

def is_admin():
    """检查是否以管理员身份运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员身份重新运行"""
    if is_admin():
        return True
    else:
        print("⚠️ 需要管理员权限，正在尝试提升权限...")
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            print("❌ 无法获取管理员权限")
            return False

def remove_readonly(func, path, excinfo):
    """移除只读属性的回调函数"""
    try:
        os.chmod(path, stat.S_IWRITE)
        func(path)
    except:
        pass

def force_delete_file(file_path):
    """强制删除文件"""
    try:
        # 方法1: 直接删除
        if os.path.exists(file_path):
            os.chmod(file_path, stat.S_IWRITE)
            os.remove(file_path)
            return True
    except:
        pass
    
    try:
        # 方法2: 使用系统命令
        subprocess.run(f'del /f /q "{file_path}"', shell=True, check=True)
        return True
    except:
        pass
    
    return False

def force_delete_directory(dir_path):
    """强制删除目录"""
    try:
        # 方法1: 使用shutil
        shutil.rmtree(dir_path, onerror=remove_readonly)
        return True
    except:
        pass
    
    try:
        # 方法2: 使用系统命令
        subprocess.run(f'rmdir /s /q "{dir_path}"', shell=True, check=True)
        return True
    except:
        pass
    
    return False

def force_delete(path):
    """强制删除文件或目录"""
    if not os.path.exists(path):
        print(f"❌ 路径不存在: {path}")
        return False
    
    print(f"🗑️ 正在删除: {path}")
    
    if os.path.isfile(path):
        success = force_delete_file(path)
    else:
        success = force_delete_directory(path)
    
    if success:
        print(f"✅ 删除成功: {path}")
        return True
    else:
        print(f"❌ 删除失败: {path}")
        return False

def setup_logging():
    """设置日志系统"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_filename = os.path.join(log_dir, f"simple_deletion_log_{datetime.now().strftime('%Y%m%d')}.log")

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger('SimpleDeleter')

def enhanced_force_delete(path, logger):
    """增强版强制删除"""
    if not os.path.exists(path):
        print(f"❌ 路径不存在: {path}")
        logger.error(f"路径不存在: {path}")
        return False

    print(f"🗑️ 正在删除: {path}")
    logger.info(f"开始删除: {path}")

    # 尝试多种删除方法
    methods = [
        ("标准删除", standard_delete),
        ("移除只读后删除", readonly_delete),
        ("系统命令删除", cmd_delete),
        ("PowerShell删除", powershell_delete),
    ]

    for method_name, method_func in methods:
        try:
            print(f"  🔄 尝试: {method_name}")
            logger.info(f"尝试方法: {method_name}")

            if method_func(path):
                print(f"✅ 删除成功: {path}")
                logger.info(f"删除成功: {path} (方法: {method_name})")
                return True
            else:
                print(f"  ❌ {method_name} 失败")
                logger.warning(f"{method_name} 失败: {path}")

        except Exception as e:
            print(f"  ❌ {method_name} 异常: {str(e)}")
            logger.error(f"{method_name} 异常: {path} - {str(e)}")

    print(f"❌ 所有删除方法都失败: {path}")
    logger.error(f"所有删除方法都失败: {path}")
    return False

def standard_delete(path):
    """标准删除方法"""
    if os.path.isfile(path):
        os.remove(path)
    else:
        shutil.rmtree(path)
    return not os.path.exists(path)

def readonly_delete(path):
    """移除只读属性后删除"""
    if os.path.isfile(path):
        os.chmod(path, stat.S_IWRITE)
        os.remove(path)
    else:
        for root, dirs, files in os.walk(path):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    os.chmod(file_path, stat.S_IWRITE)
                except:
                    pass
        shutil.rmtree(path)
    return not os.path.exists(path)

def cmd_delete(path):
    """使用系统命令删除"""
    if os.path.isfile(path):
        cmd = f'del /f /q "{path}"'
    else:
        cmd = f'rmdir /s /q "{path}"'

    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    return result.returncode == 0 and not os.path.exists(path)

def powershell_delete(path):
    """使用PowerShell删除"""
    if os.path.isfile(path):
        cmd = f'powershell -Command "Remove-Item -Path \\"{path}\\" -Force"'
    else:
        cmd = f'powershell -Command "Remove-Item -Path \\"{path}\\" -Recurse -Force"'

    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    return result.returncode == 0 and not os.path.exists(path)

def main():
    """主函数"""
    print("🗑️ Windows强制删除工具 (增强版)")
    print("=" * 50)

    # 设置日志
    logger = setup_logging()
    logger.info("程序启动")

    # 检查管理员权限
    admin_status = is_admin()
    if not admin_status:
        print("⚠️ 建议以管理员身份运行以获得最佳效果")
        choice = input("是否以管理员身份重新运行？(y/n): ").lower()
        if choice == 'y':
            if not run_as_admin():
                sys.exit(1)
            return
    else:
        print("✅ 管理员权限已获取")

    logger.info(f"管理员模式: {admin_status}")

    print("\n使用方法:")
    print("1. 输入文件或文件夹的完整路径")
    print("2. 输入 'quit' 或 'exit' 退出程序")
    print("3. 支持拖拽文件到命令行窗口")
    print("4. 所有操作都会记录到日志文件")
    print("-" * 50)

    success_count = 0
    failed_count = 0

    while True:
        try:
            path = input("\n请输入要删除的路径: ").strip()

            if path.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                logger.info(f"程序结束 - 成功: {success_count}, 失败: {failed_count}")
                break

            if not path:
                continue

            # 处理拖拽文件的引号
            path = path.strip('"\'')

            # 确认删除
            print(f"\n⚠️ 即将删除: {path}")
            confirm = input("确认删除？(y/n): ").lower()

            if confirm == 'y':
                start_time = time.time()
                if enhanced_force_delete(path, logger):
                    success_count += 1
                    elapsed = time.time() - start_time
                    print(f"⏱️ 删除耗时: {elapsed:.2f}秒")
                else:
                    failed_count += 1
            else:
                print("❌ 取消删除")
                logger.info(f"用户取消删除: {path}")

        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            logger.info("程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            logger.error(f"程序异常: {e}")
            failed_count += 1

if __name__ == "__main__":
    main()
