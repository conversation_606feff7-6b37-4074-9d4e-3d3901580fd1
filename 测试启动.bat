@echo off
echo 测试启动脚本
echo.

echo 检查Python...
python --version
if errorlevel 1 (
    echo Python未安装
    pause
    exit
)

echo.
echo Python检查完成
echo.

echo 1. 图形界面版本
echo 2. 命令行版本
echo 3. 系统权限删除工具
echo.

set /p choice=请选择: 

if "%choice%"=="1" (
    echo 启动图形界面...
    python file_permission_deleter.py
)

if "%choice%"=="2" (
    echo 启动命令行...
    python simple_force_delete.py
)

if "%choice%"=="3" (
    echo 启动系统权限工具...
    python 系统权限删除工具.py
)

pause
