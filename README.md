# Windows文件权限删除工具集

## 🎯 工具概述

这是一套专门解决Windows文件权限删除问题的工具集，特别针对"拒绝访问"、"需要权限"等系统权限问题。

## 🚀 快速开始

### 推荐使用方式（针对系统权限问题）

**方式1: 直接运行系统权限删除工具**
```bash
python 系统权限删除工具.py
```

**方式2: 使用快捷启动脚本**
```bash
双击: 系统权限删除-快捷启动.bat
```

**方式3: 使用简化启动脚本**
```bash
双击: 启动工具-简化版.bat
选择: 3. 系统权限删除工具
```

## 📁 文件说明

### 🔧 主要工具
- **`系统权限删除工具.py`** ⭐ - 专门解决系统权限问题（推荐）
- **`file_permission_deleter.py`** - 图形界面版本（功能最全）
- **`simple_force_delete.py`** - 命令行版本（简单快速）

### 🚀 启动脚本
- **`系统权限删除-快捷启动.bat`** ⭐ - 直接启动系统权限工具（推荐）
- **`启动工具-简化版.bat`** - 简化版启动菜单
- **`启动删除工具.bat`** - 完整版启动菜单（可能有兼容性问题）
- **`测试启动.bat`** - 基础测试版本

### 📋 文档和测试
- **`使用说明.md`** - 详细使用指南
- **`故障排除指南.md`** - 问题解决方案
- **`测试工具.py`** - 功能测试脚本
- **`快速测试权限删除.py`** - 权限删除测试

### 📦 配置文件
- **`requirements.txt`** - Python依赖包
- **`logs/`** - 日志文件目录
- **`reports/`** - 操作报告目录

## 🔧 解决的问题

✅ **系统权限问题**
- [WinError 5] 拒绝访问
- 需要来自TrustedInstaller的权限
- 需要来自SYSTEM的权限
- 您需要权限来执行此操作

✅ **文件占用问题**
- 文件正在使用中
- 另一个程序正在使用此文件

✅ **属性问题**
- 只读文件无法删除
- 隐藏文件无法删除

## 💡 使用建议

### 对于系统权限问题（如您遇到的情况）
1. **首选**: 使用 `系统权限删除工具.py`
2. **确保**: 以管理员身份运行
3. **如果失败**: 重启后再试
4. **查看**: 详细日志了解失败原因

### 启动脚本问题排查
如果批处理文件启动失败：
1. 尝试 `系统权限删除-快捷启动.bat`
2. 或直接运行 `python 系统权限删除工具.py`
3. 检查是否以管理员身份运行
4. 确认Python已正确安装

## 🎉 成功案例

✅ **搜狗输入法删除** - 已验证可以成功删除
✅ **系统保护文件** - 可以处理TrustedInstaller权限
✅ **顽固软件残留** - 多种删除方法自动尝试

## 📞 技术支持

如果遇到问题：
1. 查看 `故障排除指南.md`
2. 检查 `logs/` 目录中的日志文件
3. 尝试不同的启动方式
4. 确保以管理员身份运行

## ⚠️ 注意事项

- 删除操作不可撤销，请谨慎使用
- 建议删除重要文件前先备份
- 某些系统关键文件可能无法删除（这是正常的保护机制）
- 以管理员身份运行可获得最佳效果

---

**推荐工作流程**:
1. 双击 `系统权限删除-快捷启动.bat`
2. 输入要删除的文件/目录路径
3. 确认删除
4. 查看删除结果和日志
