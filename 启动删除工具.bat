@echo off
chcp 65001 >nul
title Windows文件权限删除工具

echo.
echo ========================================
echo    Windows文件权限删除工具 (增强版)
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测正常

REM 检查并安装依赖
echo 🔍 检查依赖包...
python -c "import psutil" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 缺少psutil依赖包，正在安装...
    python -m pip install psutil >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败，将使用基础功能
        echo 您可以手动运行: python -m pip install psutil
    ) else (
        echo ✅ 依赖安装成功
    )
) else (
    echo ✅ 依赖检查完成
)

echo.
echo 请选择要使用的工具:
echo 1. 图形界面版本 (推荐，功能最全)
echo 2. 命令行版本 (简单快速)
echo 3. 系统权限删除工具 (专门解决权限问题)
echo 4. 安装/更新依赖
echo 5. 查看日志目录
echo 6. 退出
echo.

set /p choice=请输入选择 (1-6):

if "%choice%"=="1" (
    echo.
    echo 🚀 启动图形界面版本...
    python file_permission_deleter.py
    goto end
)
if "%choice%"=="2" (
    echo.
    echo 🚀 启动命令行版本...
    python simple_force_delete.py
    goto end
)
if "%choice%"=="3" (
    echo.
    echo 🔑 启动系统权限删除工具...
    python "系统权限删除工具.py"
    goto end
)
if "%choice%"=="4" (
    echo.
    echo 📦 安装/更新依赖...
    python -m pip install -r requirements.txt
    echo 依赖安装完成
    goto end
)
if "%choice%"=="5" (
    echo.
    echo 📋 打开日志目录...
    if exist "logs" (
        explorer logs
    ) else (
        echo 日志目录不存在，请先运行程序
    )
    goto end
)
if "%choice%"=="6" (
    echo 👋 再见！
    exit /b 0
)

echo ❌ 无效选择，请重新运行

:end

echo.
pause
