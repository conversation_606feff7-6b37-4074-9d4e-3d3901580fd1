@echo off
chcp 65001 >nul
title Windows文件权限删除工具

echo.
echo ========================================
echo    Windows文件权限删除工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测正常
echo.

echo 请选择要使用的工具:
echo 1. 图形界面版本 (推荐)
echo 2. 命令行版本 (简单快速)
echo 3. 退出
echo.

set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 🚀 启动图形界面版本...
    python file_permission_deleter.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 启动命令行版本...
    python simple_force_delete.py
) else if "%choice%"=="3" (
    echo 👋 再见！
    exit /b 0
) else (
    echo ❌ 无效选择，请重新运行
)

echo.
pause
