@echo off
chcp 65001 >nul
title 系统权限删除工具 - 快捷启动

echo.
echo ========================================
echo      系统权限删除工具 - 快捷启动
echo ========================================
echo.
echo 🔑 专门解决Windows系统权限问题
echo 💪 解决"拒绝访问"、"需要权限"等错误
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python
    pause
    exit /b 1
)

REM 检查工具文件
if not exist "系统权限删除工具.py" (
    echo ❌ 找不到系统权限删除工具.py
    pause
    exit /b 1
)

echo ✅ 环境检查完成
echo.

REM 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 建议以管理员身份运行以获得最佳效果
    echo.
    set /p continue=是否继续？(y/n): 
    if /i not "%continue%"=="y" exit /b 0
)

echo 🚀 启动系统权限删除工具...
echo.

python 系统权限删除工具.py

echo.
echo 程序执行完成
pause
