#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建图标文件
生成一个简单的删除工具图标
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

import os

def create_simple_icon():
    """创建简单的文本图标（不需要PIL）"""
    # 创建一个简单的ICO文件内容（16x16像素）
    # 这是一个最小的ICO文件格式
    ico_data = bytes([
        # ICO文件头
        0x00, 0x00,  # 保留字段
        0x01, 0x00,  # 图像类型 (1 = ICO)
        0x01, 0x00,  # 图像数量
        
        # 图像目录条目
        0x10,        # 宽度 (16)
        0x10,        # 高度 (16)
        0x00,        # 颜色数 (0 = 256色以上)
        0x00,        # 保留字段
        0x01, 0x00,  # 颜色平面数
        0x20, 0x00,  # 每像素位数 (32位)
        0x00, 0x04, 0x00, 0x00,  # 图像数据大小
        0x16, 0x00, 0x00, 0x00,  # 图像数据偏移
        
        # 位图信息头
        0x28, 0x00, 0x00, 0x00,  # 信息头大小
        0x10, 0x00, 0x00, 0x00,  # 图像宽度
        0x20, 0x00, 0x00, 0x00,  # 图像高度 (包括掩码)
        0x01, 0x00,              # 颜色平面数
        0x20, 0x00,              # 每像素位数
        0x00, 0x00, 0x00, 0x00,  # 压缩方式
        0x00, 0x00, 0x00, 0x00,  # 图像数据大小
        0x00, 0x00, 0x00, 0x00,  # 水平分辨率
        0x00, 0x00, 0x00, 0x00,  # 垂直分辨率
        0x00, 0x00, 0x00, 0x00,  # 颜色数
        0x00, 0x00, 0x00, 0x00,  # 重要颜色数
    ])
    
    # 创建16x16的像素数据 (红色删除图标)
    pixels = []
    for y in range(16):
        for x in range(16):
            # 创建一个简单的删除图标样式
            if (x == 0 or x == 15 or y == 0 or y == 15 or  # 边框
                (x >= 4 and x <= 11 and y >= 6 and y <= 9) or  # 水平线
                (x >= 7 and x <= 8 and y >= 3 and y <= 12)):   # 垂直线
                # 红色像素 (BGRA格式)
                pixels.extend([0x00, 0x00, 0xFF, 0xFF])  # 红色
            else:
                # 透明像素
                pixels.extend([0x00, 0x00, 0x00, 0x00])
    
    # 添加AND掩码 (全部为0，表示不透明)
    and_mask = [0x00] * 32  # 16x16位 = 32字节
    
    return ico_data + bytes(pixels) + bytes(and_mask)

def create_advanced_icon():
    """使用PIL创建高质量图标"""
    if not PIL_AVAILABLE:
        return None
    
    # 创建32x32的图像
    size = 32
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制删除图标
    # 外圆
    draw.ellipse([2, 2, size-2, size-2], fill=(220, 53, 69, 255), outline=(180, 40, 55, 255), width=2)
    
    # 删除符号 (X)
    line_width = 3
    margin = 8
    draw.line([margin, margin, size-margin, size-margin], fill=(255, 255, 255, 255), width=line_width)
    draw.line([margin, size-margin, size-margin, margin], fill=(255, 255, 255, 255), width=line_width)
    
    return img

def main():
    """主函数"""
    print("🎨 创建图标文件...")
    
    icon_created = False
    
    # 尝试创建高质量图标
    if PIL_AVAILABLE:
        try:
            print("使用PIL创建高质量图标...")
            img = create_advanced_icon()
            if img:
                # 保存为ICO文件
                img.save('icon.ico', format='ICO', sizes=[(32, 32), (16, 16)])
                print("✅ 高质量图标创建成功: icon.ico")
                icon_created = True
        except Exception as e:
            print(f"⚠️ PIL图标创建失败: {e}")
    
    # 如果PIL不可用或失败，创建简单图标
    if not icon_created:
        print("创建简单图标...")
        try:
            ico_data = create_simple_icon()
            with open('icon.ico', 'wb') as f:
                f.write(ico_data)
            print("✅ 简单图标创建成功: icon.ico")
            icon_created = True
        except Exception as e:
            print(f"❌ 简单图标创建失败: {e}")
    
    if icon_created:
        print("\n📋 图标文件信息:")
        if os.path.exists('icon.ico'):
            size = os.path.getsize('icon.ico')
            print(f"  文件名: icon.ico")
            print(f"  大小: {size} 字节")
            print(f"  位置: {os.path.abspath('icon.ico')}")
        
        print("\n💡 使用方法:")
        print("  图标文件已创建，打包时会自动使用")
        print("  如需自定义图标，请替换 icon.ico 文件")
    else:
        print("❌ 图标创建失败")
        print("💡 建议:")
        print("  1. 安装PIL: pip install Pillow")
        print("  2. 或手动提供 icon.ico 文件")

if __name__ == "__main__":
    main()
