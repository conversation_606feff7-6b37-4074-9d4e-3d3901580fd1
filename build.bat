@echo off
chcp 65001 >nul
title 系统权限删除工具 - 打包脚本

echo.
echo ========================================
echo    系统权限删除工具 - 打包脚本
echo ========================================
echo.

REM 检查Python和pyinstaller
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python
    pause
    exit /b 1
)

python -c "import pyinstaller" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 未检测到pyinstaller，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ pyinstaller安装失败
        pause
        exit /b 1
    )
)

echo ✅ 环境检查完成
echo.

echo 选择打包方式:
echo 1. 图形界面版本 (推荐，无控制台窗口)
echo 2. 混合版本 (图形界面+控制台输出)
echo 3. 命令行版本 (仅控制台)
echo 4. 调试版本 (详细输出)
echo.

set /p choice=请选择 (1-4):

if "%choice%"=="1" (
    echo.
    echo 🚀 开始打包图形界面版本...
    echo 特点: 启动时直接显示图形界面，无控制台窗口
    pyinstaller --onefile --noconsole --name "系统权限删除工具" --clean 系统权限删除工具.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 开始打包混合版本...
    echo 特点: 图形界面+控制台输出，便于调试
    pyinstaller --onefile --console --name "系统权限删除工具-混合版" --clean 系统权限删除工具.py
) else if "%choice%"=="3" (
    echo.
    echo 🚀 开始打包命令行版本...
    echo 特点: 强制使用命令行模式
    pyinstaller --onefile --console --name "系统权限删除工具-命令行版" --clean --add-data "README.md;." 系统权限删除工具.py
) else if "%choice%"=="4" (
    echo.
    echo 🚀 开始打包调试版本...
    echo 特点: 包含详细调试信息
    pyinstaller --onefile --console --name "系统权限删除工具-调试版" --clean --debug all 系统权限删除工具.py
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo ✅ 打包完成！
echo 📁 输出目录: dist\
echo.

if exist "dist\" (
    echo 生成的文件:
    dir /b dist\*.exe
    echo.
    set /p open=是否打开输出目录？(y/n): 
    if /i "%open%"=="y" explorer dist
)

pause
