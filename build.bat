@echo off
chcp 65001 >nul
title 系统权限删除工具 - 打包脚本

echo.
echo ========================================
echo    系统权限删除工具 - 打包脚本
echo ========================================
echo.

REM 检查Python和pyinstaller
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python
    pause
    exit /b 1
)

python -c "import pyinstaller" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 未检测到pyinstaller，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ pyinstaller安装失败
        pause
        exit /b 1
    )
)

echo ✅ 环境检查完成
echo.

echo 选择打包方式:
echo 1. 标准版本 (带控制台，推荐)
echo 2. 静默版本 (无控制台)
echo 3. 调试版本 (详细输出)
echo.

set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 🚀 开始打包标准版本...
    pyinstaller --onefile --console --name "系统权限删除工具" --clean 系统权限删除工具.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 开始打包静默版本...
    pyinstaller --onefile --noconsole --name "系统权限删除工具-静默版" --clean 系统权限删除工具.py
) else if "%choice%"=="3" (
    echo.
    echo 🚀 开始打包调试版本...
    pyinstaller --onefile --console --name "系统权限删除工具-调试版" --clean --debug all 系统权限删除工具.py
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo ✅ 打包完成！
echo 📁 输出目录: dist\
echo.

if exist "dist\" (
    echo 生成的文件:
    dir /b dist\*.exe
    echo.
    set /p open=是否打开输出目录？(y/n): 
    if /i "%open%"=="y" explorer dist
)

pause
