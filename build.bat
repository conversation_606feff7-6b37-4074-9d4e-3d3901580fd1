@echo off
chcp 65001 >nul
title 系统权限删除工具 - 打包脚本

echo.
echo ========================================
echo    系统权限删除工具 - 打包脚本
echo ========================================
echo.

REM 检查Python和pyinstaller
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python
    pause
    exit /b 1
)

python -c "import pyinstaller" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 未检测到pyinstaller，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ pyinstaller安装失败
        pause
        exit /b 1
    )
)

echo ✅ 环境检查完成
echo.

echo 开始打包图形界面版本...
echo 命令: pyinstaller --onefile --noconsole --name "系统权限删除工具" 系统权限删除工具.py
echo.

pyinstaller --onefile --noconsole --name "系统权限删除工具" --clean 系统权限删除工具.py

if errorlevel 1 (
    echo.
    echo 打包失败，尝试调试版本...
    pyinstaller --onefile --console --name "系统权限删除工具-调试版" --clean 系统权限删除工具.py
)

echo.
if exist "dist\系统权限删除工具.exe" (
    echo ✅ 打包成功！
    echo 📁 文件位置: dist\系统权限删除工具.exe
    echo.
    echo 使用方法:
    echo 1. 直接双击exe文件启动图形界面
    echo 2. 拖拽文件到exe上进行删除
    echo 3. 命令行: "系统权限删除工具.exe" "文件路径"
    echo.
    explorer dist
) else if exist "dist\系统权限删除工具-调试版.exe" (
    echo ✅ 调试版本打包成功！
    echo 📁 文件位置: dist\系统权限删除工具-调试版.exe
    echo.
    explorer dist
) else (
    echo ❌ 打包失败，请检查错误信息
)

pause
