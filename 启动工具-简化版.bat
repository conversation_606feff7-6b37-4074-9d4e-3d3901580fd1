@echo off
chcp 65001 >nul
title Windows文件权限删除工具

echo.
echo ========================================
echo    Windows文件权限删除工具 (增强版)
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测正常
echo.

:menu
echo 请选择要使用的工具:
echo.
echo 1. 图形界面版本 (推荐，功能最全)
echo 2. 命令行版本 (简单快速)  
echo 3. 系统权限删除工具 (专门解决权限问题) ⭐推荐⭐
echo 4. 安装依赖包
echo 5. 查看日志目录
echo 6. 退出
echo.

set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto gui
if "%choice%"=="2" goto cli
if "%choice%"=="3" goto system
if "%choice%"=="4" goto install
if "%choice%"=="5" goto logs
if "%choice%"=="6" goto exit
goto invalid

:gui
echo.
echo 🚀 启动图形界面版本...
python file_permission_deleter.py
goto end

:cli
echo.
echo 🚀 启动命令行版本...
python simple_force_delete.py
goto end

:system
echo.
echo 🔑 启动系统权限删除工具...
python 系统权限删除工具.py
goto end

:install
echo.
echo 📦 安装依赖包...
python -m pip install psutil
if errorlevel 1 (
    echo ❌ 安装失败，请检查网络连接
) else (
    echo ✅ 安装成功
)
goto end

:logs
echo.
echo 📋 打开日志目录...
if exist "logs" (
    explorer logs
    echo ✅ 已打开日志目录
) else (
    echo ⚠️ 日志目录不存在，请先运行程序
)
goto end

:invalid
echo.
echo ❌ 无效选择，请重新选择
echo.
goto menu

:exit
echo.
echo 👋 再见！
exit /b 0

:end
echo.
echo 程序执行完成
pause
goto menu
