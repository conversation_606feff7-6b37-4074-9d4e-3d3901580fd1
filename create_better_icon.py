#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建更好的图标文件
使用ASCII艺术创建一个更精美的删除工具图标
"""

def create_better_icon():
    """创建更好的ICO文件"""
    
    # ICO文件头
    ico_header = bytes([
        0x00, 0x00,  # 保留字段
        0x01, 0x00,  # 图像类型 (1 = ICO)
        0x02, 0x00,  # 图像数量 (16x16 和 32x32)
    ])
    
    # 16x16图像目录条目
    dir_entry_16 = bytes([
        0x10,        # 宽度 (16)
        0x10,        # 高度 (16)
        0x00,        # 颜色数 (0 = 256色以上)
        0x00,        # 保留字段
        0x01, 0x00,  # 颜色平面数
        0x20, 0x00,  # 每像素位数 (32位)
        0x00, 0x04, 0x00, 0x00,  # 图像数据大小 (1024字节)
        0x16, 0x00, 0x00, 0x00,  # 图像数据偏移
    ])
    
    # 32x32图像目录条目
    dir_entry_32 = bytes([
        0x20,        # 宽度 (32)
        0x20,        # 高度 (32)
        0x00,        # 颜色数
        0x00,        # 保留字段
        0x01, 0x00,  # 颜色平面数
        0x20, 0x00,  # 每像素位数 (32位)
        0x00, 0x10, 0x00, 0x00,  # 图像数据大小 (4096字节)
        0x16, 0x04, 0x00, 0x00,  # 图像数据偏移
    ])
    
    # 创建16x16像素数据
    pixels_16 = create_16x16_pixels()
    
    # 创建32x32像素数据
    pixels_32 = create_32x32_pixels()
    
    # 组合所有数据
    ico_data = ico_header + dir_entry_16 + dir_entry_32 + pixels_16 + pixels_32
    
    return ico_data

def create_16x16_pixels():
    """创建16x16的像素数据"""
    # 定义颜色
    RED = [0x00, 0x00, 0xFF, 0xFF]      # 红色 (BGRA)
    WHITE = [0xFF, 0xFF, 0xFF, 0xFF]    # 白色
    DARK_RED = [0x00, 0x00, 0xCC, 0xFF] # 深红色
    TRANSPARENT = [0x00, 0x00, 0x00, 0x00] # 透明
    
    # 16x16删除图标模式
    pattern = [
        "0000000000000000",
        "0000000000000000",
        "0011111111111100",
        "0111111111111110",
        "1111100110011111",
        "1111110110111111",
        "1111111001111111",
        "1111111001111111",
        "1111111001111111",
        "1111111001111111",
        "1111110110111111",
        "1111100110011111",
        "0111111111111110",
        "0011111111111100",
        "0000000000000000",
        "0000000000000000",
    ]
    
    pixels = []
    for row in pattern:
        for char in row:
            if char == '0':
                pixels.extend(TRANSPARENT)
            elif char == '1':
                pixels.extend(RED)
            elif char == '2':
                pixels.extend(WHITE)
            elif char == '3':
                pixels.extend(DARK_RED)
    
    # 添加位图信息头 (40字节)
    bitmap_header = bytes([
        0x28, 0x00, 0x00, 0x00,  # 信息头大小
        0x10, 0x00, 0x00, 0x00,  # 图像宽度
        0x20, 0x00, 0x00, 0x00,  # 图像高度 (包括掩码)
        0x01, 0x00,              # 颜色平面数
        0x20, 0x00,              # 每像素位数
        0x00, 0x00, 0x00, 0x00,  # 压缩方式
        0x00, 0x04, 0x00, 0x00,  # 图像数据大小
        0x00, 0x00, 0x00, 0x00,  # 水平分辨率
        0x00, 0x00, 0x00, 0x00,  # 垂直分辨率
        0x00, 0x00, 0x00, 0x00,  # 颜色数
        0x00, 0x00, 0x00, 0x00,  # 重要颜色数
    ])
    
    # AND掩码 (全部为0，表示不透明)
    and_mask = [0x00] * 32  # 16x16位 = 32字节
    
    return bitmap_header + bytes(pixels) + bytes(and_mask)

def create_32x32_pixels():
    """创建32x32的像素数据"""
    # 定义颜色
    RED = [0x00, 0x00, 0xFF, 0xFF]      # 红色
    WHITE = [0xFF, 0xFF, 0xFF, 0xFF]    # 白色
    DARK_RED = [0x00, 0x00, 0xCC, 0xFF] # 深红色
    LIGHT_RED = [0x66, 0x66, 0xFF, 0xFF] # 浅红色
    TRANSPARENT = [0x00, 0x00, 0x00, 0x00] # 透明
    
    # 32x32删除图标模式（更精细）
    pattern = [
        "00000000000000000000000000000000",
        "00000000000000000000000000000000",
        "00000000000000000000000000000000",
        "00000333333333333333333333330000",
        "00003111111111111111111111113000",
        "00031111111111111111111111111300",
        "00311111111111111111111111111130",
        "03111111100000000000001111111113",
        "31111111000000000000000111111113",
        "31111110000000000000000011111113",
        "31111100000000000000000001111113",
        "31111000000000000000000000111113",
        "31110000000000000000000000011113",
        "31100000000000000000000000001113",
        "31000000000000000000000000000113",
        "31000000000000000000000000000113",
        "31000000000000000000000000000113",
        "31000000000000000000000000000113",
        "31100000000000000000000000001113",
        "31110000000000000000000000011113",
        "31111000000000000000000000111113",
        "31111100000000000000000001111113",
        "31111110000000000000000011111113",
        "31111111000000000000000111111113",
        "03111111100000000000001111111113",
        "00311111111111111111111111111130",
        "00031111111111111111111111111300",
        "00003111111111111111111111113000",
        "00000333333333333333333333330000",
        "00000000000000000000000000000000",
        "00000000000000000000000000000000",
        "00000000000000000000000000000000",
    ]
    
    pixels = []
    for row in pattern:
        for char in row:
            if char == '0':
                pixels.extend(TRANSPARENT)
            elif char == '1':
                pixels.extend(RED)
            elif char == '2':
                pixels.extend(WHITE)
            elif char == '3':
                pixels.extend(DARK_RED)
            elif char == '4':
                pixels.extend(LIGHT_RED)
    
    # 添加位图信息头 (40字节)
    bitmap_header = bytes([
        0x28, 0x00, 0x00, 0x00,  # 信息头大小
        0x20, 0x00, 0x00, 0x00,  # 图像宽度
        0x40, 0x00, 0x00, 0x00,  # 图像高度 (包括掩码)
        0x01, 0x00,              # 颜色平面数
        0x20, 0x00,              # 每像素位数
        0x00, 0x00, 0x00, 0x00,  # 压缩方式
        0x00, 0x10, 0x00, 0x00,  # 图像数据大小
        0x00, 0x00, 0x00, 0x00,  # 水平分辨率
        0x00, 0x00, 0x00, 0x00,  # 垂直分辨率
        0x00, 0x00, 0x00, 0x00,  # 颜色数
        0x00, 0x00, 0x00, 0x00,  # 重要颜色数
    ])
    
    # AND掩码 (全部为0，表示不透明)
    and_mask = [0x00] * 128  # 32x32位 = 128字节
    
    return bitmap_header + bytes(pixels) + bytes(and_mask)

def main():
    """主函数"""
    print("🎨 创建高质量图标文件...")
    
    try:
        ico_data = create_better_icon()
        
        # 备份原图标
        if os.path.exists('icon.ico'):
            import shutil
            shutil.copy('icon.ico', 'icon_backup.ico')
            print("📦 已备份原图标为 icon_backup.ico")
        
        # 保存新图标
        with open('icon.ico', 'wb') as f:
            f.write(ico_data)
        
        print("✅ 高质量图标创建成功: icon.ico")
        
        if os.path.exists('icon.ico'):
            size = os.path.getsize('icon.ico')
            print(f"📋 图标信息:")
            print(f"  文件大小: {size} 字节")
            print(f"  包含尺寸: 16x16, 32x32")
            print(f"  颜色深度: 32位 (带透明度)")
        
    except Exception as e:
        print(f"❌ 创建图标失败: {e}")

if __name__ == "__main__":
    import os
    main()
