#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜狗输入法专用删除工具
专门解决搜狗输入法顽固文件删除问题
"""

import os
import sys
import subprocess
import time
import shutil
import stat
import ctypes
from pathlib import Path

def is_admin():
    """检查是否以管理员身份运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员身份重新运行"""
    if is_admin():
        return True
    else:
        print("⚠️ 需要管理员权限，正在尝试提升权限...")
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            print("❌ 无法获取管理员权限")
            return False

def stop_sogou_processes():
    """停止所有搜狗相关进程"""
    print("🔄 正在停止搜狗输入法相关进程...")
    
    sogou_processes = [
        "SogouCloud.exe",
        "SogouExe.exe", 
        "SogouInput.exe",
        "SogouTSF.exe",
        "SogouPY.exe",
        "SogouPinyin.exe",
        "SogouUniProxy.exe",
        "SogouCloudInput.exe",
        "SogouCloudInputProxy.exe"
    ]
    
    stopped_count = 0
    for process in sogou_processes:
        try:
            result = subprocess.run(f'taskkill /f /im "{process}"', 
                                  shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ 已停止: {process}")
                stopped_count += 1
            else:
                print(f"  ℹ️ 未运行: {process}")
        except Exception as e:
            print(f"  ❌ 停止失败: {process} - {e}")
    
    print(f"📊 共停止 {stopped_count} 个进程")
    
    # 等待进程完全结束
    print("⏳ 等待进程完全结束...")
    time.sleep(3)

def disable_sogou_services():
    """禁用搜狗输入法相关服务"""
    print("🛑 正在禁用搜狗输入法相关服务...")
    
    sogou_services = [
        "SogouCloudService",
        "SogouInputService", 
        "SogouUpdateService"
    ]
    
    for service in sogou_services:
        try:
            # 停止服务
            subprocess.run(f'sc stop "{service}"', shell=True, capture_output=True)
            # 禁用服务
            subprocess.run(f'sc config "{service}" start= disabled', shell=True, capture_output=True)
            print(f"  ✅ 已禁用服务: {service}")
        except:
            print(f"  ℹ️ 服务不存在或已禁用: {service}")

def remove_readonly_recursive(path):
    """递归移除只读属性"""
    try:
        if os.path.isfile(path):
            os.chmod(path, stat.S_IWRITE)
        else:
            for root, dirs, files in os.walk(path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        os.chmod(file_path, stat.S_IWRITE)
                    except:
                        pass
                for dir in dirs:
                    dir_path = os.path.join(root, dir)
                    try:
                        os.chmod(dir_path, stat.S_IWRITE)
                    except:
                        pass
    except Exception as e:
        print(f"  ⚠️ 移除只读属性失败: {e}")

def force_delete_sogou(sogou_path):
    """强力删除搜狗输入法目录"""
    if not os.path.exists(sogou_path):
        print(f"❌ 路径不存在: {sogou_path}")
        return False
    
    print(f"🗑️ 开始删除搜狗输入法: {sogou_path}")
    
    # 方法1: 获取文件所有权
    print("  🔧 方法1: 获取文件所有权...")
    try:
        takeown_cmd = f'takeown /f "{sogou_path}" /r /d y'
        subprocess.run(takeown_cmd, shell=True, capture_output=True)
        
        icacls_cmd = f'icacls "{sogou_path}" /grant %username%:F /t /c'
        subprocess.run(icacls_cmd, shell=True, capture_output=True)
        print("  ✅ 获取所有权成功")
    except Exception as e:
        print(f"  ❌ 获取所有权失败: {e}")
    
    # 方法2: 移除只读属性
    print("  🔧 方法2: 移除只读属性...")
    remove_readonly_recursive(sogou_path)
    
    # 方法3: 使用robocopy清空目录
    print("  🔧 方法3: 使用robocopy清空...")
    try:
        import tempfile
        empty_dir = tempfile.mkdtemp()
        
        robocopy_cmd = f'robocopy "{empty_dir}" "{sogou_path}" /mir /r:0 /w:0'
        result = subprocess.run(robocopy_cmd, shell=True, capture_output=True)
        
        os.rmdir(empty_dir)
        
        if result.returncode <= 7:  # robocopy成功的返回码
            print("  ✅ robocopy清空成功")
            try:
                os.rmdir(sogou_path)
                print("  ✅ 目录删除成功")
                return True
            except:
                pass
    except Exception as e:
        print(f"  ❌ robocopy方法失败: {e}")
    
    # 方法4: 系统命令强制删除
    print("  🔧 方法4: 系统命令强制删除...")
    try:
        cmd = f'rmdir /s /q "{sogou_path}"'
        result = subprocess.run(cmd, shell=True, capture_output=True)
        if result.returncode == 0 and not os.path.exists(sogou_path):
            print("  ✅ 系统命令删除成功")
            return True
    except Exception as e:
        print(f"  ❌ 系统命令删除失败: {e}")
    
    # 方法5: PowerShell删除
    print("  🔧 方法5: PowerShell删除...")
    try:
        ps_cmd = f'powershell -Command "Remove-Item -Path \\"{sogou_path}\\" -Recurse -Force"'
        result = subprocess.run(ps_cmd, shell=True, capture_output=True)
        if result.returncode == 0 and not os.path.exists(sogou_path):
            print("  ✅ PowerShell删除成功")
            return True
    except Exception as e:
        print(f"  ❌ PowerShell删除失败: {e}")
    
    # 方法6: 移动到临时目录
    print("  🔧 方法6: 移动到临时目录...")
    try:
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_name = f"sogou_deleted_{int(time.time())}"
        temp_path = os.path.join(temp_dir, temp_name)
        
        shutil.move(sogou_path, temp_path)
        print(f"  ✅ 已移动到: {temp_path}")
        print("  ℹ️ 重启后可手动删除临时文件")
        return True
    except Exception as e:
        print(f"  ❌ 移动失败: {e}")
    
    return False

def clean_registry():
    """清理搜狗输入法注册表项"""
    print("🧹 清理注册表项...")
    
    registry_keys = [
        r"HKEY_CURRENT_USER\Software\SogouInput",
        r"HKEY_LOCAL_MACHINE\SOFTWARE\SogouInput",
        r"HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\SogouInput"
    ]
    
    for key in registry_keys:
        try:
            cmd = f'reg delete "{key}" /f'
            subprocess.run(cmd, shell=True, capture_output=True)
            print(f"  ✅ 已清理: {key}")
        except:
            print(f"  ℹ️ 注册表项不存在: {key}")

def main():
    """主函数"""
    print("🗑️ 搜狗输入法专用删除工具")
    print("=" * 50)
    
    # 检查管理员权限
    if not is_admin():
        print("⚠️ 需要管理员权限才能完全删除搜狗输入法")
        choice = input("是否以管理员身份重新运行？(y/n): ").lower()
        if choice == 'y':
            if not run_as_admin():
                sys.exit(1)
            return
        else:
            print("❌ 没有管理员权限，可能无法完全删除")
    else:
        print("✅ 管理员权限已获取")
    
    # 获取搜狗输入法路径
    if len(sys.argv) > 1:
        sogou_path = sys.argv[1].strip('"\'')
    else:
        sogou_path = input("\n请输入搜狗输入法安装路径: ").strip('"\'')
    
    if not sogou_path:
        print("❌ 未提供路径")
        return
    
    print(f"\n🎯 目标路径: {sogou_path}")
    
    # 确认删除
    confirm = input("\n⚠️ 确认删除搜狗输入法？(y/n): ").lower()
    if confirm != 'y':
        print("❌ 取消删除")
        return
    
    print("\n🚀 开始删除过程...")
    
    # 步骤1: 停止进程
    stop_sogou_processes()
    
    # 步骤2: 禁用服务
    disable_sogou_services()
    
    # 步骤3: 删除文件
    success = force_delete_sogou(sogou_path)
    
    # 步骤4: 清理注册表
    clean_registry()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 搜狗输入法删除成功！")
        print("💡 建议重启计算机以确保完全清理")
    else:
        print("❌ 删除未完全成功")
        print("💡 建议:")
        print("   1. 重启计算机后重新运行此工具")
        print("   2. 在安全模式下运行此工具")
        print("   3. 使用第三方卸载工具")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
