系统权限文件删除工具 - 使用说明
========================================

🎯 功能介绍
专门解决Windows系统权限问题导致的文件无法删除，如：
- [WinError 5] 拒绝访问
- 需要来自TrustedInstaller的权限
- 需要来自SYSTEM的权限
- 文件正在使用中

🚀 快速开始

1. 安装依赖
   pip install -r requirements.txt

2. 直接运行（推荐）
   python 系统权限删除工具.py
   → 自动启动图形界面

3. 命令行使用
   python 系统权限删除工具.py "文件路径"
   → 直接删除指定文件

4. 打包成exe
   build.bat
   → 自动创建图标并生成独立的exe文件

🖥️ 图形界面功能

左侧面板：
- 选择文件/文件夹（支持多选）
- 删除选项设置
- 诊断文件状态
- 开始删除操作

右侧面板：
- 实时操作日志
- 彩色状态显示
- 日志保存功能

🔧 核心特性

✅ 自动获取文件所有权 (takeown)
✅ 设置完全控制权限 (icacls)
✅ 移除只读属性
✅ 结束占用进程（需要psutil）
✅ 8种删除方法自动尝试
✅ 详细的操作日志
✅ 批量文件处理

⚠️ 注意事项

- 建议以管理员身份运行
- 删除操作不可撤销，请谨慎使用
- 重要文件建议先备份
- 不要删除系统关键文件

📋 故障排除

Q: 图形界面无法启动？
A: 使用命令行模式：python 系统权限删除工具.py --cli

Q: 删除仍然失败？
A: 1. 确保以管理员身份运行
   2. 重启计算机后重试
   3. 查看logs目录中的详细日志

Q: 缺少psutil库？
A: pip install psutil

📞 技术支持

- 查看logs目录中的详细日志文件
- 使用诊断功能分析文件状态
- 确保以管理员身份运行程序
