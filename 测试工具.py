#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除工具测试脚本
用于验证工具功能是否正常
"""

import os
import sys
import tempfile
import stat
from pathlib import Path

def create_test_files():
    """创建测试文件"""
    print("🔧 创建测试文件...")
    
    # 创建测试目录
    test_dir = "test_files"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    test_files = []
    
    # 1. 普通文件
    normal_file = os.path.join(test_dir, "normal_file.txt")
    with open(normal_file, 'w', encoding='utf-8') as f:
        f.write("这是一个普通测试文件")
    test_files.append(normal_file)
    print(f"✅ 创建普通文件: {normal_file}")
    
    # 2. 只读文件
    readonly_file = os.path.join(test_dir, "readonly_file.txt")
    with open(readonly_file, 'w', encoding='utf-8') as f:
        f.write("这是一个只读测试文件")
    os.chmod(readonly_file, stat.S_IREAD)
    test_files.append(readonly_file)
    print(f"✅ 创建只读文件: {readonly_file}")
    
    # 3. 嵌套目录
    nested_dir = os.path.join(test_dir, "nested_dir")
    os.makedirs(nested_dir, exist_ok=True)
    nested_file = os.path.join(nested_dir, "nested_file.txt")
    with open(nested_file, 'w', encoding='utf-8') as f:
        f.write("这是嵌套目录中的文件")
    test_files.append(nested_dir)
    print(f"✅ 创建嵌套目录: {nested_dir}")
    
    # 4. 长文件名
    long_name = "a" * 100 + ".txt"
    long_file = os.path.join(test_dir, long_name)
    with open(long_file, 'w', encoding='utf-8') as f:
        f.write("这是一个长文件名的测试文件")
    test_files.append(long_file)
    print(f"✅ 创建长文件名: {long_file}")
    
    # 5. 特殊字符文件名
    special_file = os.path.join(test_dir, "特殊字符文件@#$%^&().txt")
    with open(special_file, 'w', encoding='utf-8') as f:
        f.write("这是包含特殊字符的文件名")
    test_files.append(special_file)
    print(f"✅ 创建特殊字符文件: {special_file}")
    
    return test_files

def test_import():
    """测试导入功能"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试标准库
        import tkinter
        print("✅ tkinter 导入成功")
    except ImportError:
        print("❌ tkinter 导入失败")
    
    try:
        # 测试psutil
        import psutil
        print("✅ psutil 导入成功")
        print(f"  版本: {psutil.__version__}")
    except ImportError:
        print("⚠️ psutil 未安装，部分功能将不可用")
    
    try:
        # 测试主程序导入
        sys.path.insert(0, '.')
        import file_permission_deleter
        print("✅ 主程序模块导入成功")
    except Exception as e:
        print(f"❌ 主程序模块导入失败: {e}")

def test_permissions():
    """测试权限"""
    print("🔐 测试权限...")
    
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if is_admin:
            print("✅ 当前以管理员身份运行")
        else:
            print("⚠️ 当前以普通用户身份运行")
            print("  建议以管理员身份运行以获得最佳效果")
    except:
        print("❌ 无法检测管理员权限")

def test_file_operations():
    """测试文件操作"""
    print("📁 测试文件操作...")
    
    # 创建临时文件
    temp_file = "temp_test_file.txt"
    try:
        with open(temp_file, 'w') as f:
            f.write("临时测试文件")
        print("✅ 文件创建成功")
        
        # 测试读取
        with open(temp_file, 'r') as f:
            content = f.read()
        print("✅ 文件读取成功")
        
        # 测试删除
        os.remove(temp_file)
        print("✅ 文件删除成功")
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")

def test_system_commands():
    """测试系统命令"""
    print("💻 测试系统命令...")
    
    import subprocess
    
    # 测试基本命令
    try:
        result = subprocess.run('echo test', shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 基本命令执行成功")
        else:
            print("❌ 基本命令执行失败")
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
    
    # 测试PowerShell
    try:
        result = subprocess.run('powershell -Command "Write-Host test"', 
                              shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ PowerShell 可用")
        else:
            print("⚠️ PowerShell 可能不可用")
    except Exception as e:
        print(f"⚠️ PowerShell 测试异常: {e}")

def main():
    """主测试函数"""
    print("🧪 Windows文件权限删除工具 - 功能测试")
    print("=" * 60)
    
    # 运行各项测试
    test_import()
    print()
    
    test_permissions()
    print()
    
    test_file_operations()
    print()
    
    test_system_commands()
    print()
    
    # 询问是否创建测试文件
    print("📋 测试完成！")
    print()
    choice = input("是否创建测试文件用于删除测试？(y/n): ").lower()
    
    if choice == 'y':
        test_files = create_test_files()
        print()
        print("🎯 测试文件创建完成！")
        print("现在您可以使用删除工具测试这些文件的删除功能")
        print()
        print("测试文件列表:")
        for file in test_files:
            print(f"  - {file}")
        print()
        print("建议测试步骤:")
        print("1. 启动图形界面工具")
        print("2. 选择test_files目录")
        print("3. 使用诊断功能查看文件信息")
        print("4. 尝试删除文件")
        print("5. 查看日志文件了解详细过程")
    else:
        print("跳过测试文件创建")
    
    print()
    print("🚀 您现在可以运行主程序进行测试！")

if __name__ == "__main__":
    main()
